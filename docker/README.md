# Docker Setup for Tukxi Microservices

This directory contains Docker configurations for the Tukxi application microservices architecture.

## Architecture Overview

The application consists of the following services:

### Application Services
- **API** (Port 3000): Main REST API service
- **Kafka Consumers** (Port 3001): Handles Kafka message consumption
- **Notifier** (Port 3002): Manages notifications
- **Ride Matcher** (Port 3003): Handles ride matching logic

### Infrastructure Services
- **PostgreSQL** (Port 5432): Primary database
- **Redis** (Port 6379): Caching and session storage
- **Kafka** (Port 9092): Message streaming platform (KRaft mode - no ZooKeeper needed)
- **RabbitMQ** (Port 5672, Management UI: 15672): Message broker

## Quick Start

### Development Environment

```bash
# Start all services
./scripts/docker-dev.sh start

# Start only infrastructure services
./scripts/docker-dev.sh start-infra

# Start only microservices
./scripts/docker-dev.sh start-micro

# View logs
./scripts/docker-dev.sh logs

# Stop all services
./scripts/docker-dev.sh stop
```

### Production Environment

```bash
# Start all services in production mode
./scripts/docker-prod.sh start

# Scale a service
./scripts/docker-prod.sh scale api 3

# Update services with zero-downtime
./scripts/docker-prod.sh update

# Backup data
./scripts/docker-prod.sh backup
```

## Docker Files

### Development Dockerfiles
- `api.dev.Dockerfile`: Development API service
- `kafka-consumers.dev.Dockerfile`: Development Kafka consumers
- `notifier.dev.Dockerfile`: Development notifier service
- `ride-matcher.dev.Dockerfile`: Development ride matcher

### Production Dockerfiles
- `api.prod.Dockerfile`: Production API service (multi-stage build)
- `kafka-consumers.prod.Dockerfile`: Production Kafka consumers
- `notifier.prod.Dockerfile`: Production notifier service
- `ride-matcher.prod.Dockerfile`: Production ride matcher

## Environment Variables

The services use the following key environment variables:

### Database
- `DATABASE_HOST`: PostgreSQL host (default: pgsql)
- `DATABASE_PORT`: PostgreSQL port (default: 5432)
- `DATABASE_USER`: Database user
- `DATABASE_PASSWORD`: Database password

### Redis
- `REDIS_HOST`: Redis host (default: redis)
- `REDIS_PORT`: Redis port (default: 6379)

### Kafka
- `KAFKA_BROKERS`: Kafka broker addresses (default: kafka:9092)
- `KAFKA_CLIENT_ID`: Kafka client identifier
- `KAFKA_GROUP_ID`: Kafka consumer group
- `KAFKA_CONNECTION_TIMEOUT`: Connection timeout (default: 10000)
- `KAFKA_REQUEST_TIMEOUT`: Request timeout (default: 30000)

### RabbitMQ
- `RABBITMQ_URL`: RabbitMQ connection URL
- `RABBITMQ_QUEUE`: Queue name
- `RABBITMQ_EXCHANGE`: Exchange name

## Service Ports

| Service | Development Port | Production Port |
|---------|------------------|-----------------|
| API | 3000 | 3000 |
| Kafka Consumers | 3001 | 3001 |
| Notifier | 3002 | 3002 |
| Ride Matcher | 3003 | 3003 |
| PostgreSQL | 5432 | 5432 |
| Redis | 6379 | 6379 |
| Kafka | 9092 | 9092 |
| RabbitMQ | 5672 | 5672 |
| RabbitMQ Management | 15672 | 15672 |

## Health Checks

All production services include health checks:
- **API**: `GET /health`
- **Microservices**: Custom health endpoints
- **PostgreSQL**: `pg_isready`
- **Redis**: `redis-cli ping`
- **Kafka**: `kafka-broker-api-versions`
- **RabbitMQ**: `rabbitmq-diagnostics ping`

## Data Persistence

The following volumes are used for data persistence:
- `pgsql_data`: PostgreSQL data
- `redis_data`: Redis data
- `kafka_data`: Kafka logs and data (KRaft mode)
- `rabbitmq_data`: RabbitMQ data

## Networking

All services communicate through a custom bridge network `tukxi-network` for:
- Service discovery
- Isolated communication
- Better security

## Resource Limits (Production)

| Service | Memory Limit | Memory Reservation |
|---------|--------------|-------------------|
| API | 512M | 256M |
| Kafka Consumers | 256M | 128M |
| Notifier | 256M | 128M |
| Ride Matcher | 256M | 128M |
| PostgreSQL | 512M | 256M |
| Redis | 128M | 64M |
| Kafka (KRaft) | 512M | 256M |
| RabbitMQ | 256M | 128M |

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000-3003, 5432, 6379, 9092, 5672, 15672 are available
2. **Memory issues**: Increase Docker memory allocation if services fail to start
3. **Network issues**: Check if `tukxi-network` exists and services can communicate

### Useful Commands

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs -f [service-name]

# Execute commands in containers
docker-compose exec [service-name] [command]

# Rebuild specific service
docker-compose build --no-cache [service-name]

# Remove all containers and volumes
docker-compose down -v --remove-orphans
```

### Monitoring

- **RabbitMQ Management UI**: http://localhost:15672 (user: tukxi, pass: password)
- **Service Health**: Check individual service health endpoints
- **Logs**: Use the provided scripts to view aggregated logs

## Security Considerations

### Development
- Default passwords are used for convenience
- All ports are exposed for debugging

### Production
- Change default passwords
- Use environment variables for secrets
- Consider using Docker secrets
- Implement proper firewall rules
- Use TLS for external communications
