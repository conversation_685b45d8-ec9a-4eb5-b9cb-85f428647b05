FROM node:22-alpine AS build

# Enable corepack and pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

WORKDIR /usr/src/app

# Copy package files
COPY ./backend/package.json ./backend/pnpm-lock.yaml ./
COPY ./backend/nest-cli.json ./backend/tsconfig.json ./backend/tsconfig.build.json ./

# Copy source code
COPY ./backend/apps ./apps
COPY ./backend/libs ./libs
COPY ./backend/prisma ./prisma

# Install dependencies
RUN pnpm install

# Generate Prisma client
RUN pnpm prisma:generate

EXPOSE 3002

# Start the notifier service
CMD ["pnpm", "start:notifier:dev"]
