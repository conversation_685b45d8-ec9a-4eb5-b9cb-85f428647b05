import { BaseEntity } from '../base.repository';
import { UserProfile } from './userProfile.model';

export enum DriverVehicleDocumentStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export interface DriverVehicleDocument extends BaseEntity {
  driverVehicleId: string;
  vehicleDocumentId: string;
  createdBy: string;
  details: any;
  documentUrl?: string | null;
  documentFields?: any | null;
  status: DriverVehicleDocumentStatus;
  rejectionNote?: string | null;
  // Relations
  driverVehicle?: any; // Replace 'any' with DriverVeh<PERSON> if available
  vehicleDocument?: any; // Replace 'any' with VehicleDocument if available
  createdByUser?: UserProfile;
}
