import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { Language } from './models/language.model';

@Injectable()
export class LanguageRepository extends BaseRepository<Language> {
  protected readonly modelName = 'language';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createLanguage(
    data: Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Language> {
    return this.create(data);
  }

  async findAllLanguages(): Promise<Language[]> {
    return this.findMany();
  }

  async findLanguageById(id: string): Promise<Language | null> {
    return this.findById(id);
  }

  async findLanguageByName(name: string): Promise<Language | null> {
    return this.findOne({ where: { name } });
  }

  async findLanguageByCode(code: string): Promise<Language | null> {
    return this.findOne({ where: { code } });
  }

  async updateLanguage(id: string, data: Partial<Language>): Promise<Language> {
    return this.updateById(id, data);
  }

  async deleteLanguage(id: string): Promise<Language> {
    return this.softDeleteById(id);
  }

  async paginateLanguages(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }

  async permanentDeleteLanguage(id: string): Promise<Language> {
    return this.hardDeleteById(id);
  }
}
