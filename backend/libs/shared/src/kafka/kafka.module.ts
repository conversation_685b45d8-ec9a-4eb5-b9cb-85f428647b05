import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { KafkaProducerService } from './kafka-producer.service';
import { AppConfigModule, AppConfigService } from '../config';
import { AuditLogConsumer } from './consumers/auditlog.consumer';

@Module({
  imports: [
    AppConfigModule,
    ClientsModule.registerAsync([
      {
        name: 'KAFKA_SERVICE',
        useFactory: (config: AppConfigService) => ({
          transport: Transport.KAFKA,
          options: {
            client: {
              clientId: config.kafkaClientId,
              brokers: config.kafkaBrokers,
            },
            producerOnlyMode: true,
          },
        }),
        inject: [AppConfigService],
      },
    ]),
  ],
  controllers: [AuditLogConsumer],
  providers: [KafkaProducerService],
  exports: [KafkaProducerService],
})
export class KafkaModule {}
