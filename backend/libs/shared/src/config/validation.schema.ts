import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // Server Configuration
  PORT: Joi.number().default(3000),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),

  // Database Configuration
  DATABASE_URL: Joi.string().required(),

  // Redis Configuration
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().allow('').default(''),
  REDIS_DB: Joi.number().default(0),

  // Notification Services
  ENGAGESPOT_API_KEY: Joi.string().required(),
  ENGAGESPOT_API_SECRET: Joi.string().required(),
  EN_VERIFICATION_CODE_WORKFLOW: Joi.string().required(),
  EN_FORGOT_PASSWORD_WORKFLOW: Joi.string().required(),

  // JWT Configuration
  JWT_SECRET: Joi.string().required().min(32),
  JWT_ACCESS_TOKEN_EXPIRY: Joi.string().default('15m'),
  JWT_REFRESH_TOKEN_EXPIRY: Joi.string().default('7d'),

  // Google OAuth Configuration
  GOOGLE_CLIENT_ID: Joi.string().required(),
  GOOGLE_CLIENT_SECRET: Joi.string().required(),
  GOOGLE_CLIENT_ID_IOS: Joi.string().required(),

  // Apple OAuth Configuration
  APPLE_BUNDLE_ID: Joi.string().required(),

  // OTP Configuration
  OTP_LENGTH: Joi.number().min(4).max(8).default(6),
  OTP_EXPIRY_MINUTES: Joi.number().min(1).max(60).default(5),
  OTP_WINDOW: Joi.number().min(0).max(5).default(1),

  // Rate Limiting Configuration
  AUTH_RATE_LIMIT_TTL: Joi.number().default(900), // 15 minutes
  AUTH_RATE_LIMIT_MAX: Joi.number().default(5), // 5 attempts

  // AWS Configuration
  AWS_ACCESS_KEY_ID: Joi.string().required(),
  AWS_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_REGION: Joi.string().required(),
  AWS_BUCKET_NAME: Joi.string().required(),

  // Cashfree Configuration
  // CASHFREE_BASE_URL: Joi.string().uri().required(),
  CASHFREE_CLIENT_SECRET: Joi.string().required(),
  CASHFREE_CLIENT_ID: Joi.string().required(),
  CASHFREE_ENVIRONMENT: Joi.string().valid('production', 'sandbox').required(),

  // Microservices Configuration

  // Kafka Configuration
  KAFKA_BROKERS: Joi.string().default('localhost:9092'),
  KAFKA_CLIENT_ID: Joi.string().default('tukxi-kafka-client'),
  KAFKA_GROUP_ID: Joi.string().default('tukxi-consumer-group'),
  KAFKA_RETRY_ATTEMPTS: Joi.number().min(1).max(10).default(5),
  KAFKA_RETRY_DELAY: Joi.number().min(100).max(10000).default(1000),
  KAFKA_CONNECTION_TIMEOUT: Joi.number().min(1000).max(60000).default(10000),
  KAFKA_REQUEST_TIMEOUT: Joi.number().min(1000).max(120000).default(30000),

  // RabbitMQ Configuration
  RABBITMQ_URL: Joi.string().default('amqp://localhost:5672'),
  RABBITMQ_QUEUE: Joi.string().default('tukxi_queue'),
  RABBITMQ_EXCHANGE: Joi.string().default('tukxi_exchange'),
  RABBITMQ_ROUTING_KEY: Joi.string().default('tukxi.ride.match'),
  RABBITMQ_DURABLE: Joi.boolean().default(true),
  RABBITMQ_PREFETCH_COUNT: Joi.number().min(1).max(100).default(10),

  // Microservice Ports
  KAFKA_CONSUMER_PORT: Joi.number().min(1000).max(65535).default(3001),
  NOTIFIER_PORT: Joi.number().min(1000).max(65535).default(3002),
  RIDE_MATCHER_PORT: Joi.number().min(1000).max(65535).default(3003),

  // Health Check Configuration
  HEALTH_CHECK_TIMEOUT: Joi.number().min(1000).max(30000).default(5000),
  HEALTH_CHECK_INTERVAL: Joi.number().min(5000).max(300000).default(30000),

  // Logging Configuration
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info'),
  LOG_FORMAT: Joi.string().valid('json', 'simple', 'combined').default('json'),

  // Graceful Shutdown Configuration
  SHUTDOWN_TIMEOUT: Joi.number().min(1000).max(60000).default(10000),
});
