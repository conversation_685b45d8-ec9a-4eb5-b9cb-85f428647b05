import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ProductRepository } from '../../repositories/product.repository';
import { Product } from '../../repositories/models/product.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { VehicleTypeService } from '../vehicle-type/vehicle-type.service';
import { ProductServiceService } from '../product-service/product-service.service';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class ProductService {
  constructor(
    private readonly productRepository: ProductRepository,
    private readonly vehicleTypeService: VehicleTypeService,
    private readonly productServiceService: ProductServiceService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  /**
   * Create a new product.
   * @param data - Product data excluding id and timestamps
   */
  async createProduct(
    data: Omit<
      Product,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'isEnabled'
    > & { isEnabled?: boolean },
  ): Promise<Product> {
    // Validate that the vehicle type exists
    await this.vehicleTypeService.findVehicleTypeById(data.vehicleTypeId);

    // Validate that the product service exists if provided
    if (data.productServiceId) {
      await this.productServiceService.findProductServiceById(
        data.productServiceId,
      );
    }

    // Check for duplicate name
    const existingByName = await this.productRepository.findOne({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive',
        },
      },
    });
    if (existingByName) {
      throw new BadRequestException(
        `Product with name "${data.name}" already exists`,
      );
    }

    // Generate identifier if not provided
    let identifier = data.identifier;
    if (!identifier) {
      identifier = data.name.toLowerCase().replace(/\s+/g, '_');
    }

    // Check for duplicate identifier
    const existingByIdentifier =
      await this.productRepository.findProductByIdentifier(identifier);
    if (existingByIdentifier) {
      throw new BadRequestException(
        `Product with identifier "${identifier}" already exists`,
      );
    }

    const product = await this.productRepository.createProduct({
      ...data,
      identifier,
      isEnabled: data.isEnabled ?? true,
      passengerLimit: data.passengerLimit ?? 0,
    });

    return this.addSignedUrlToProduct(product);
  }

  /**
   * Find all products.
   */
  async findAllProducts(): Promise<Product[]> {
    const products = await this.productRepository.findAllProducts();
    return this.addSignedUrlsToProducts(products);
  }

  /**
   * Find product by ID.
   * @param id - Product ID
   */
  async findProductById(id: string): Promise<Product> {
    const product = await this.productRepository.findProductById(id);
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return this.addSignedUrlToProduct(product);
  }

  /**
   * Update product by ID.
   * @param id - Product ID
   * @param data - Partial product data
   */
  async updateProduct(id: string, data: Partial<Product>): Promise<Product> {
    // Check if product exists
    const existingProduct = await this.productRepository.findProductById(id);
    if (!existingProduct) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // If vehicleTypeId is being updated, validate it exists
    if (data.vehicleTypeId) {
      await this.vehicleTypeService.findVehicleTypeById(data.vehicleTypeId);
    }

    // If productServiceId is being updated, validate it exists
    if (data.productServiceId) {
      await this.productServiceService.findProductServiceById(
        data.productServiceId,
      );
    }

    // Check for duplicate name (excluding current record)
    if (data.name) {
      const existingByName = await this.productRepository.findOne({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive',
          },
        },
        select: { id: true },
      });
      if (existingByName && existingByName.id !== id) {
        throw new BadRequestException(
          `Product with name "${data.name}" already exists`,
        );
      }
    }

    // Don't allow identifier to be changed
    if (data.identifier !== undefined) {
      delete data.identifier;
    }

    // Handle icon deletion if icon is being updated and old icon exists
    if (
      data.icon !== undefined &&
      existingProduct.icon &&
      data.icon !== existingProduct.icon
    ) {
      try {
        await this.fileUploadService.deleteFile(existingProduct.icon);
      } catch (error) {
        console.warn(
          `Failed to delete old icon file: ${existingProduct.icon}`,
          error,
        );
      }
    }

    const updatedProduct = await this.productRepository.updateProduct(id, data);
    return this.addSignedUrlToProduct(updatedProduct);
  }

  /**
   * Delete product by ID (soft delete).
   * @param id - Product ID
   */
  async deleteProduct(id: string): Promise<Product> {
    // Check if product exists
    await this.findProductById(id);

    return this.productRepository.deleteProduct(id);
  }

  /**
   * Get paginated products.
   * @param paginationDto - Pagination parameters
   */
  async paginateProducts(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;
    return this.productRepository.paginateProducts(page, limit);
  }

  /**
   * Find products by vehicle type ID.
   * @param vehicleTypeId - Vehicle type ID
   */
  async findProductsByVehicleTypeId(vehicleTypeId: string): Promise<Product[]> {
    // Validate that the vehicle type exists
    await this.vehicleTypeService.findVehicleTypeById(vehicleTypeId);

    const products =
      await this.productRepository.findProductsByVehicleTypeId(vehicleTypeId);
    return this.addSignedUrlsToProducts(products);
  }

  /**
   * Enable product and optionally enable all city products
   * @param id - Product ID
   * @param enableAllCityProducts - Whether to enable all city products
   */
  async enableProduct(
    id: string,
    enableAllCityProducts = false,
  ): Promise<Product> {
    const product = await this.productRepository.findProductById(id);
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // Enable the product
    const updatedProduct = await this.productRepository.updateProduct(id, {
      isEnabled: true,
    });

    // // Enable all city products if requested
    if (enableAllCityProducts) {
      //   await this.productRepository.enableAllCityProducts(id);
    }

    return this.addSignedUrlToProduct(updatedProduct);
  }

  /**
   * Disable product and disable all city products
   * @param id - Product ID
   */
  async disableProduct(id: string): Promise<Product> {
    const product = await this.productRepository.findProductById(id);
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // Disable the product
    const updatedProduct = await this.productRepository.updateProduct(id, {
      isEnabled: false,
    });

    // Disable all city products with this product ID
    // await this.productRepository.disableAllCityProducts(id);

    return this.addSignedUrlToProduct(updatedProduct);
  }

  /**
   * Helper method to add signed URL to single product
   */
  private async addSignedUrlToProduct(product: Product): Promise<Product> {
    if (product.icon) {
      try {
        const signedUrl = await this.fileUploadService.getSignedUrl(
          product.icon,
          3600, // 1 hour expiry
        );
        return { ...product, icon: signedUrl };
      } catch (error) {
        // If signed URL generation fails, keep the original URL
        return product;
      }
    }
    return product;
  }

  /**
   * Helper method to add signed URLs to array of products
   */
  private async addSignedUrlsToProducts(
    products: Product[],
  ): Promise<Product[]> {
    return Promise.all(
      products.map(async (product) => {
        return this.addSignedUrlToProduct(product);
      }),
    );
  }
}
