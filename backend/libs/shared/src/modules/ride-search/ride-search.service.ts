import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { LocationIngestorService } from '../location/location-ingestor.service';
import {
  GoogleRouteMatrixService,
  Location,
} from '@shared/shared/common/google/google-route-matrix.service';
import { DriverMetadata } from '@shared/shared/repositories/models/redis/driverLocation.model';
import { ProductService } from '../product/product.service';
import { Product } from '@shared/shared/repositories/models/product.model';
import { ProductService as ProductServiceModel } from '@shared/shared/repositories/models/productService.model';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';

// Enums for better type safety
export enum ServiceType {
  INTERCITY = 'intercity',
  CITY_RENTAL = 'city_rental',
  CITY_RIDE = 'city_ride',
}

export enum RideBookingType {
  NOW = 'now',
  LATER = 'later',
}

// Type alias for backward compatibility with controller
export type RideSearchType = RideBookingType;

// Input/Output interfaces
export interface SearchRideParams {
  pickup: {
    lat: number;
    lng: number;
  };
  destination: {
    lat: number;
    lng: number;
  };
  type?: RideBookingType | undefined;
  pickupTime?: string | undefined;
}

export interface RideSearchResult {
  id: string;
  name: string;
  identifier: string;
  serviceName: string;
  description?: string | undefined;
  icon?: string | undefined; // Full S3 URL
  price: number; // Dummy/hardcoded for now
  estimatedArrivalTimePickup: string; // ETA to pickup in minutes
  distanceToDestination: string; // Distance to destination in meters
  estimatedArrivalTimeDestination: string; // ISO string of estimated arrival at destination
}

// Internal service types
export interface ProductWithRelations extends Product {
  productService?: ProductServiceModel;
  vehicleType?: VehicleType;
}

export interface NearbyDriver {
  driverId: string;
  metadata: DriverMetadata | null;
}

export interface DriverRouteData {
  driverId: string;
  etaToPickupMinutes: number;
  distanceToPickupKm: number;
  estimatedArrivalTime: string;
}

export interface RouteComputationResult {
  distanceMeters?: number | undefined;
  duration?: {
    seconds: number;
  } | undefined;
}

export interface ZoneDetectionResult {
  serviceTypes: ServiceType[];
  cityName: string;
}

export interface ProductDriverMatch {
  product: ProductWithRelations;
  driverData?: DriverRouteData;
}

@Injectable()
export class RideSearchService {
  private readonly logger = new Logger(RideSearchService.name);
  private readonly H3_RESOLUTION = 8; // Resolution 8 for zone matching
  // private readonly DEFAULT_DRIVER_LIMIT = 10;
  private readonly DEFAULT_PRICE = 100; // Dummy price for now

  constructor(
    private readonly h3UtilityService: H3UtilityService,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly locationIngestorService: LocationIngestorService,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    private readonly productService: ProductService,
  ) {}

  /**
   * Main method to search for available rides
   */
  async searchRides(params: SearchRideParams): Promise<RideSearchResult[]> {
    this.logger.log('Starting ride search...');

    // Step 1: Validate input parameters
    this.validatePickupTime(params);

    // Step 2: Determine service types based on pickup and destination zones
    const zoneResult = await this.determineServiceTypes(params);

    // Step 3: Get available products for the determined service types
    const products = await this.getProductsForServiceTypes(zoneResult.serviceTypes);

    if (products.length === 0) {
      this.logger.log('No enabled products found for the service types');
      return [];
    }

    // Step 4: Compute pickup to destination route
    const pickupToDestinationResult = await this.computePickupToDestinationRoute(params);

    // Step 5: Handle "later" bookings (return products without driver matching)
    if (params.type === RideBookingType.LATER) {
      return this.mapProductsToRideSearchResults(products, pickupToDestinationResult);
    }

    // Step 6: Find nearby drivers and compute routes for "now" bookings
    const driverRouteData = await this.findNearbyDriversWithRoutes(
      params,
      zoneResult.cityName,
      pickupToDestinationResult
    );

    // Step 7: If no drivers found, return products without driver data
    if (driverRouteData.length === 0) {
      this.logger.log('No drivers with valid location data found');
      return this.mapProductsToRideSearchResults(products, pickupToDestinationResult);
    }

    // Step 8: Match products with best available drivers
    const productDriverMatches = this.getBestDriverForProducts(products, driverRouteData);

    // Step 9: Build and return results
    const results = this.buildRideSearchResults(productDriverMatches, pickupToDestinationResult);

    this.logger.log(`Returning ${results.length} ride options`);
    return results;
  }

  /**
   * Validates pickup time for later bookings
   */
  private validatePickupTime(params: SearchRideParams): void {
    if (params.type === RideBookingType.LATER) {
      if (!params.pickupTime) {
        throw new BadRequestException('Pickup time is required when type is later');
      }

      const pickupDate = new Date(params.pickupTime);
      const now = new Date();

      if (pickupDate <= now) {
        throw new BadRequestException('Pickup time must be in the future');
      }
    }
  }

  /**
   * Determines service types based on pickup and destination zones
   */
  private async determineServiceTypes(params: SearchRideParams): Promise<ZoneDetectionResult> {
    // Convert coordinates to H3 indexes
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );

    const destinationH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.destination.lat,
      params.destination.lng,
      this.H3_RESOLUTION,
    );

    this.logger.log(`Pickup H3 index: ${pickupH3Index}`);
    this.logger.log(`Destination H3 index: ${destinationH3Index}`);

    // Check if pickup is in a city zone
    const pickupH3BigInt = this.h3UtilityService.stringToH3Index(pickupH3Index);
    const pickupCityZone = await this.h3IndexToZoneRepository.findFirstCityZonesByH3Index(pickupH3BigInt);

    let serviceTypes: ServiceType[] = [ServiceType.INTERCITY]; // Default to intercity
    let cityName = '';

    if (pickupCityZone) {
      cityName = pickupCityZone.zone?.name || 'Unknown';
      this.logger.log(`Pickup is in city zone: ${cityName}`);

      // Check if destination is also in a city zone
      const destinationH3BigInt = this.h3UtilityService.stringToH3Index(destinationH3Index);
      const destinationCityZone = await this.h3IndexToZoneRepository.findFirstCityZonesByH3Index(destinationH3BigInt);

      if (destinationCityZone) {
        this.logger.log(`Destination is in city zone: ${destinationCityZone.zone?.name || 'Unknown'}`);
        this.logger.log('Processing city and rental services');
        serviceTypes = [ServiceType.CITY_RENTAL, ServiceType.CITY_RIDE];
      } else {
        this.logger.log('Destination is not in a city zone - intercity rides');
        serviceTypes = [ServiceType.INTERCITY];
      }
    } else {
      this.logger.log('Pickup is not in a city zone - intercity rides');
      serviceTypes = [ServiceType.INTERCITY];
    }

    return { serviceTypes, cityName };
  }

  /**
   * Fetches enabled products for the given service types
   */
  private async getProductsForServiceTypes(serviceTypes: ServiceType[]): Promise<ProductWithRelations[]> {
    const serviceIdentifiers = serviceTypes.map(type => type.toString());
    this.logger.log(`Fetching products for service types: ${serviceIdentifiers.join(', ')}`);

    return await this.productService.findEnabledProductsByServiceIdentifiers(serviceIdentifiers);
  }

  /**
   * Computes route from pickup to destination
   */
  private async computePickupToDestinationRoute(params: SearchRideParams): Promise<RouteComputationResult | null> {
    return await this.googleRouteMatrixService.computePickupToDestination(
      { lat: params.pickup.lat, lng: params.pickup.lng },
      { lat: params.destination.lat, lng: params.destination.lng },
    );
  }

  /**
   * Maps a single product to RideSearchResult
   */
  private mapProductToRideSearchResult(
    product: ProductWithRelations,
    pickupToDestinationResult: RouteComputationResult | null,
    driverData?: DriverRouteData
  ): RideSearchResult {
    // Handle missing distance gracefully
    const distanceToDestination = this.getDistanceToDestination(pickupToDestinationResult);

    // Handle missing duration gracefully
    const estimatedArrivalTime = this.getEstimatedArrivalTime(
      pickupToDestinationResult,
      driverData
    );

    return {
      id: product.id,
      identifier: product.identifier ?? '',
      name: product.name,
      serviceName: product.productService?.name || 'Unknown Service',
      description: product.description || undefined,
      icon: product.icon || undefined,
      price: this.DEFAULT_PRICE,
      estimatedArrivalTimePickup: driverData?.etaToPickupMinutes.toString() || '0',
      distanceToDestination,
      estimatedArrivalTimeDestination: estimatedArrivalTime,
    };
  }

  /**
   * Safely extracts distance from route computation result
   */
  private getDistanceToDestination(pickupToDestinationResult: RouteComputationResult | null): string {
    if (!pickupToDestinationResult ||
        pickupToDestinationResult.distanceMeters === undefined ||
        pickupToDestinationResult.distanceMeters === null) {
      return '0';
    }
    return pickupToDestinationResult.distanceMeters.toString();
  }

  /**
   * Safely calculates estimated arrival time
   */
  private getEstimatedArrivalTime(
    pickupToDestinationResult: RouteComputationResult | null,
    driverData?: DriverRouteData
  ): string {
    // If we have driver data with calculated arrival time, use it
    if (driverData?.estimatedArrivalTime) {
      return driverData.estimatedArrivalTime;
    }

    // If we have route duration, calculate arrival time
    if (pickupToDestinationResult?.duration?.seconds) {
      const arrivalTime = new Date(Date.now() + pickupToDestinationResult.duration.seconds * 1000);
      return arrivalTime.toISOString();
    }

    // Fallback to current time if no duration available
    return new Date().toISOString();
  }

  /**
   * Maps multiple products to RideSearchResults
   */
  private mapProductsToRideSearchResults(
    products: ProductWithRelations[],
    pickupToDestinationResult: RouteComputationResult | null
  ): RideSearchResult[] {
    this.logger.log(`Mapping ${products.length} products to ride search results`);

    return products.map(product =>
      this.mapProductToRideSearchResult(product, pickupToDestinationResult)
    );
  }

  /**
   * Finds nearby drivers and computes route data
   */
  private async findNearbyDriversWithRoutes(
    params: SearchRideParams,
    cityName: string,
    pickupToDestinationResult: RouteComputationResult | null
  ): Promise<DriverRouteData[]> {
    // Convert pickup coordinates to H3 index
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );

    // Find nearby drivers
    this.logger.log('Finding nearby drivers...');
    const nearbyDrivers: NearbyDriver[] = await this.locationIngestorService.findAllNearbyDrivers(
      pickupH3Index,
      6,
      cityName || undefined,
      50,
    );

    if (nearbyDrivers.length === 0) {
      this.logger.log('No drivers found in the area');
      return [];
    }

    this.logger.log(`Found ${nearbyDrivers.length} nearby drivers`);

    // Filter drivers with valid location data
    const validDrivers = nearbyDrivers.filter(
      (driver: NearbyDriver) => driver.metadata?.lat && driver.metadata?.lon
    );

    if (validDrivers.length === 0) {
      this.logger.log('No drivers with valid location data found');
      return [];
    }

    // Get driver locations for route matrix API
    const driverLocations: Location[] = validDrivers.map(
      (driver: NearbyDriver) => ({
        lat: driver.metadata!.lat,
        lng: driver.metadata!.lon,
      })
    );

    // Compute routes from drivers to pickup location
    this.logger.log('Computing route matrices...');
    const driversToPickupResults = await this.googleRouteMatrixService.computeDriversToPickup(
      driverLocations,
      { lat: params.pickup.lat, lng: params.pickup.lng },
    );

    // Calculate ETAs and distances for each driver
    const driverRouteData: DriverRouteData[] = validDrivers
      .map((driver: NearbyDriver, index: number) => {
        const routeResult = driversToPickupResults[index];

        // Handle missing route data gracefully - still include driver with default values
        const etaToPickupMinutes = routeResult?.duration?.seconds
          ? Math.ceil(routeResult.duration.seconds / 60)
          : 0; // Default to 0 minutes if no route data

        const distanceToPickupKm = routeResult?.distanceMeters
          ? routeResult.distanceMeters / 1000
          : 0; // Default to 0 km if no route data

        // Calculate estimated arrival time at destination
        const driverTripDuration = routeResult?.duration?.seconds || 0;
        const destinationTripDuration = pickupToDestinationResult?.duration?.seconds || 0;
        const totalTripDuration = driverTripDuration + destinationTripDuration;

        const estimatedArrivalTime = totalTripDuration > 0
          ? new Date(Date.now() + totalTripDuration * 1000)
          : new Date(); // Use current time if no duration data

        return {
          driverId: driver.driverId,
          etaToPickupMinutes,
          distanceToPickupKm,
          estimatedArrivalTime: estimatedArrivalTime.toISOString(),
        };
      })
      .filter((data): data is DriverRouteData => data !== null);

    this.logger.log(`Computed route data for ${driverRouteData.length} drivers`);
    return driverRouteData;
  }

  /**
   * Matches products with the best available drivers
   */
  private getBestDriverForProducts(
    products: ProductWithRelations[],
    driverRouteData: DriverRouteData[]
  ): ProductDriverMatch[] {
    const productDriverMatches: ProductDriverMatch[] = [];

    for (const product of products) {
      // Find the driver with the shortest ETA for this product
      const bestDriverForProduct = driverRouteData
        .sort((a, b) => a.etaToPickupMinutes - b.etaToPickupMinutes)[0];

      productDriverMatches.push({
        product,
        driverData: bestDriverForProduct,
      });
    }

    return productDriverMatches;
  }

  /**
   * Builds the final ride search results
   */
  private buildRideSearchResults(
    productDriverMatches: ProductDriverMatch[],
    pickupToDestinationResult: RouteComputationResult | null
  ): RideSearchResult[] {
    return productDriverMatches.map(match =>
      this.mapProductToRideSearchResult(
        match.product,
        pickupToDestinationResult,
        match.driverData
      )
    );
  }

}
