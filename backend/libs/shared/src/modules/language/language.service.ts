import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { LanguageRepository } from '../../repositories/language.repository';
import { Language } from '@shared/shared/repositories/models/language.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';

@Injectable()
export class LanguageService {
  constructor(private readonly languageRepository: LanguageRepository) {}

  async createLanguage(
    data: Omit<Language, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Language> {
    // Validate that code doesn't already exist
    if (data.code) {
      const existingLanguage = await this.languageRepository.findLanguageByCode(
        data.code,
      );
      if (existingLanguage) {
        throw new ConflictException(
          `Language with code '${data.code}' already exists`,
        );
      }
    }

    return this.languageRepository.createLanguage(data);
  }

  async findAllLanguages(): Promise<Language[]> {
    return this.languageRepository.findAllLanguages();
  }

  async findLanguageById(id: string): Promise<Language> {
    const language = await this.languageRepository.findLanguageById(id);
    if (!language)
      throw new NotFoundException(`Language with ID ${id} not found`);
    return language;
  }

  async findLanguageByName(name: string): Promise<Language | null> {
    return this.languageRepository.findLanguageByName(name);
  }

  async findLanguageByCode(code: string): Promise<Language | null> {
    return this.languageRepository.findLanguageByCode(code);
  }

  async updateLanguage(id: string, data: Partial<Language>): Promise<Language> {
    await this.findLanguageById(id); // Ensure it exists before deleting
    if (data.code) {
      const existingLanguage = await this.languageRepository.findLanguageByCode(
        data.code,
      );
      if (existingLanguage && existingLanguage.id !== id) {
        throw new ConflictException(
          `Language with code '${data.code}' already exists`,
        );
      }
    }
    return this.languageRepository.updateLanguage(id, data);
  }

  async deleteLanguage(id: string): Promise<Language> {
    await this.findLanguageById(id); // Ensure it exists before deleting
    return this.languageRepository.permanentDeleteLanguage(id);
  }

  async paginateLanguages(page = 1, limit = 10, dto: PaginationDto) {
    const options = this.buildPaginateOptions(dto);
    return this.languageRepository.paginateLanguages(page, limit, options);
  }

  /**
   * Build options for pagination, supporting search by name
   */
  private buildPaginateOptions(dto?: PaginationDto) {
    const options: any = {};
    if (dto) {
      if (dto.search) {
        options.where = {
          name: {
            contains: dto.search,
            mode: 'insensitive',
          },
        };
      }
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }
}
