import { forwardRef, Module } from '@nestjs/common';
import { UserProfileService } from './user-profile.service';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import {
  AuthCredentialRepository,
  RoleRepository,
  UserRepository,
} from '@shared/shared/repositories';
import { UserRoleRepository } from '../../repositories/role.repository';
import { CityRepository } from '../../repositories/city.repository';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { RedisModule } from '../../database/redis/redis.module';
import { UserProfileRedisService } from './user-profile-redis.service';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigModule } from '@shared/shared/config';
import { OtpService } from '../auth/services/otp.service';
import { UserRegistrationService } from '../auth/services/user-registration.service';
import { AuthModule } from '../auth/auth.module';
import { LanguageModule } from '../language/language.module';
import { KycDocumentModule } from '../kyc-document/kyc-document.module';

@Module({
  imports: [
    UserOnboardingModule,
    AppConfigModule,
    RedisModule,
    forwardRef(() => AuthModule),
    LanguageModule,
    KycDocumentModule,
  ],
  providers: [
    UserProfileRepository,
    UserProfileService,
    UserProfileRedisService,
    RoleRepository,
    UserRoleRepository,
    CityRepository,
    PrismaService,
    UserRepository,
    NotificationService,
    AuthCredentialRepository,
    OtpService,
    UserRegistrationService,
  ],
  exports: [UserProfileRepository, UserProfileService],
})
export class UserProfileModule {}
