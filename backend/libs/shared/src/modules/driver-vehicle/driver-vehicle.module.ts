import { Module } from '@nestjs/common';
import { DriverVehicleService } from './driver-vehicle.service';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { UserProfileModule } from '../user-profile/user-profile.module';
import { UserOnboardingModule } from '../user-onboarding/user-onboarding.module';
import { DriverVehicleDocumentModule } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.module';
import { VehicleDocumentRepository } from '../../repositories/vehicle-document.repository';

@Module({
  imports: [
    UserProfileModule,
    UserOnboardingModule,
    DriverVehicleDocumentModule,
  ],
  providers: [
    DriverVehicleService,
    DriverVehicleRepository,
    VehicleDocumentRepository,
    PrismaService,
  ],
  exports: [DriverVehicleService, DriverVehicleRepository],
})
export class DriverVehicleModule {}
