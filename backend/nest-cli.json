{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/backend/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/backend/tsconfig.app.json"}, "monorepo": true, "root": "apps/backend", "projects": {"api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "backend": {"type": "application", "root": "apps/backend", "entryFile": "main", "sourceRoot": "apps/backend/src", "compilerOptions": {"tsConfigPath": "apps/backend/tsconfig.app.json"}}, "kafka-consumers": {"type": "application", "root": "apps/kafka-consumers", "entryFile": "main", "sourceRoot": "apps/kafka-consumers/src", "compilerOptions": {"tsConfigPath": "apps/kafka-consumers/tsconfig.app.json"}}, "notifier": {"type": "application", "root": "apps/notifier", "entryFile": "main", "sourceRoot": "apps/notifier/src", "compilerOptions": {"tsConfigPath": "apps/notifier/tsconfig.app.json"}}, "ride-matcher": {"type": "application", "root": "apps/ride-matcher", "entryFile": "main", "sourceRoot": "apps/ride-matcher/src", "compilerOptions": {"tsConfigPath": "apps/ride-matcher/tsconfig.app.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}}}