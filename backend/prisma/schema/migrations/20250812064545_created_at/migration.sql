-- AlterTable
ALTER TABLE "DriverKyc" ADD COLUMN     "created_by" UUID;

-- AlterTable
ALTER TABLE "driver_vehicle_documents" ADD COLUMN     "created_by" UUID;

-- AddForeignKey
ALTER TABLE "DriverKyc" ADD CONSTRAINT "DriverKyc_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "user_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_vehicle_documents" ADD CONSTRAINT "driver_vehicle_documents_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "user_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;
