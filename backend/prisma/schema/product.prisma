model Product {
  id               String    @id @default(uuid()) @db.Uuid
  vehicleTypeId    String    @map("vehicle_type_id") @db.Uuid
  productServiceId String?    @map("product_service_id") @db.Uuid
  name             String
  description      String?
  identifier       String?
  icon             String?
  languageSpec     Json?     @map("language_spec") @db.JsonB
  isEnabled        Boolean   @default(true) @map("is_enabled")
  passengerLimit   Int       @default(0) @map("passenger_limit")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  vehicleType    VehicleType    @relation(fields: [vehicleTypeId], references: [id])
  productService ProductService? @relation(fields: [productServiceId], references: [id])

  cityProducts CityProduct[]

  @@index([languageSpec], name: "idx_product_language_spec")
  @@map("products")
}
