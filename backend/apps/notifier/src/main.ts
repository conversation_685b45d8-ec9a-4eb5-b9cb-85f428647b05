import { NestFactory } from '@nestjs/core';
import { NotifierModule } from './notifier.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';

async function bootstrap() {
  const logger = new Logger('Notifier');
  const app = await NestFactory.create(NotifierModule, {
    logger: ['error', 'warn', 'log'],
  });

  // Security & DX defaults
  app.use(helmet());
  app.enableCors();
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  // Optional: global prefix from config
  // const cfg = app.get(AppConfigService);
  // app.setGlobalPrefix(cfg.coreApiPrefix);

  app.enableShutdownHooks(['SIGINT', 'SIGTERM']);

  const port = Number(process.env['NOTIFIER_PORT'] ?? 3002);
  await app.listen(port);
  logger.log(`Notifier HTTP server listening on port ${port}`);
}

bootstrap().catch((e) => {
  // eslint-disable-next-line no-console
  console.error('Bootstrap failed:', e);
  process.exit(1);
});
