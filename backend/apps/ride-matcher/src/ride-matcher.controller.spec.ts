import { Test, TestingModule } from '@nestjs/testing';
import { RideMatcherController } from './ride-matcher.controller';
import { RideMatcherService } from './ride-matcher.service';

describe('RideMatcherController', () => {
  let rideMatcherController: RideMatcherController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [RideMatcherController],
      providers: [RideMatcherService],
    }).compile();

    rideMatcherController = app.get<RideMatcherController>(
      RideMatcherController,
    );
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(rideMatcherController.getHello()).toBe('Hello World!');
    });
  });
});
