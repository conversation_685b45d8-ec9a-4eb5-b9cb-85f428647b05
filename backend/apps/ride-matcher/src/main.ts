import { NestFactory } from '@nestjs/core';
import { RideMatcherModule } from './ride-matcher.module';
import { Logger } from '@nestjs/common';
import {
  AppConfigService,
  RmqServerOptionsFactory,
  maskRabbitUrl,
} from '@shared/shared';

async function bootstrap() {
  const logger = new Logger('RideMatcher');

  const app = await NestFactory.create(RideMatcherModule, {
    logger: ['error', 'warn', 'log'],
  });

  const cfg = app.get(AppConfigService);
  const rmqOptions = app.get(RmqServerOptionsFactory).create();

  logger.log('Starting Ride Matcher microservice...');
  logger.log(`RabbitMQ URL: ${maskRabbitUrl(cfg.rabbitmqUrl)}`);
  logger.log(`Queue: ${cfg.rabbitmqQueue}`);
  logger.log(`Exchange: ${cfg.rabbitmqExchange}`);
  logger.log(`Routing Key: ${cfg.rabbitmqRoutingKey}`);
  logger.log(`Durable: ${cfg.rabbitmqDurable}`);
  logger.log(`Prefetch: ${cfg.rabbitmqPrefetchCount}`);

  app.connectMicroservice(rmqOptions);
  app.enableShutdownHooks(['SIGINT', 'SIGTERM']);

  const forceExitAfter = (ms: number) =>
    setTimeout(() => {
      logger.error(`Forced shutdown after ${ms}ms timeout`);
      process.exit(1);
    }, ms).unref();

  const onSignal = (sig: string) => {
    logger.log(`${sig} received; graceful shutdown started...`);
    const timer = forceExitAfter(cfg.shutdownTimeout);
    app
      .close()
      .then(() => {
        clearTimeout(timer);
        logger.log('Ride Matcher shut down successfully');
        process.exit(0);
      })
      .catch((err) => {
        clearTimeout(timer);
        logger.error('Error during shutdown', err);
        process.exit(1);
      });
  };

  process.once('SIGINT', () => onSignal('SIGINT'));
  process.once('SIGTERM', () => onSignal('SIGTERM'));
  process.once('uncaughtException', (error) => {
    logger.error('Uncaught Exception', error);
    process.exit(1);
  });
  process.once('unhandledRejection', (reason: any, promise) => {
    logger.error(`Unhandled Rejection at: ${promise}`, reason);
    process.exit(1);
  });

  await app.startAllMicroservices();
  logger.log('Ride Matcher microservice is listening for messages');
}
bootstrap().catch((e) => {
  // eslint-disable-next-line no-console
  console.error('Bootstrap failed:', e);
  process.exit(1);
});
