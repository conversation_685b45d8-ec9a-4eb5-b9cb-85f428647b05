import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { CreateDriverVehicleDto } from './dto/create-driver-vehicle.dto';
import { UploadNocDto } from './dto/upload-noc.dto';
import { CreateAdminDriverVehicleDto } from './dto/create-admin-driver-vehicle.dto';
import { UpdateAdminDriverVehicleDto } from './dto/update-admin-driver-vehicle.dto';
import { DriverVehicleResponseDto } from './dto/driver-vehicle-response.dto';
import { ChangeDriverVehicleStatusDto } from './dto/change-vehicle-status.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { DriverVehicleService } from '@shared/shared/modules/driver-vehicle/driver-vehicle.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { DriverVehicleDocumentService } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.service';

@ApiTags('Driver Vehicles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('driver-vehicles')
export class DriverVehicleController {
  constructor(
    private readonly driverVehicleService: DriverVehicleService,
    private readonly driverVehicleDocumentService: DriverVehicleDocumentService,
  ) {}

  // ==================== ADMIN ENDPOINTS ====================

  @Post('admin/create')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create driver vehicle (Admin)',
    description:
      'Admin endpoint to create a driver vehicle for a specific profile. Validates that no vehicle with the same vehicle type exists for the profile.',
  })
  @ApiResponse({
    status: 201,
    description: 'Driver vehicle created successfully',
    type: ApiResponseDto<DriverVehicleResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Vehicle type already exists for profile or invalid data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async createDriverVehicleForAdmin(
    @Body() createAdminDriverVehicleDto: CreateAdminDriverVehicleDto,
  ) {
    const driverVehicle =
      await this.driverVehicleService.createDriverVehicleForAdmin(
        createAdminDriverVehicleDto,
      );
    return {
      success: true,
      message: 'Driver vehicle created successfully',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Patch('admin/update/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update driver vehicle (Admin)',
    description:
      'Admin endpoint to update driver vehicle details. Validates vehicle type conflicts if vehicle type is being changed.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle updated successfully',
    type: ApiResponseDto<DriverVehicleResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Vehicle type already exists for profile or invalid data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async updateDriverVehicleForAdmin(
    @Param('id') id: string,
    @Body() updateAdminDriverVehicleDto: UpdateAdminDriverVehicleDto,
  ) {
    const driverVehicle =
      await this.driverVehicleService.updateDriverVehicleForAdmin(
        id,
        updateAdminDriverVehicleDto,
      );
    return {
      success: true,
      message: 'Driver vehicle updated successfully',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Get('admin/list/user-profile/:profileId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List driver vehicles by profile ID (Admin)',
    description:
      'Admin endpoint to get all driver vehicles for a specific profile with related vehicle type and city product information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicles retrieved successfully',
    type: ApiResponseDto<DriverVehicleResponseDto[]>,
  })
  @ApiResponse({
    status: 404,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async listDriverVehiclesForAdmin(@Param('profileId') profileId: string) {
    const driverVehicles =
      await this.driverVehicleService.findDriverVehiclesByUserProfileId(
        profileId,
      );
    return {
      success: true,
      message: 'Driver vehicles retrieved successfully',
      data: driverVehicles,
      timestamp: Date.now(),
    };
  }

  // ==================== REGULAR ENDPOINTS ====================

  @Patch('vehicle')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a driver vehicle for the onboarded user',
    description:
      'Create or update a driver vehicle for the authenticated user. If a vehicle with the same type already exists, it will be updated.',
  })
  @ApiResponse({
    status: 201,
    description: 'Driver vehicle created successfully',
    type: ApiResponseDto<DriverVehicleResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
    type: ApiErrorResponseDto,
  })
  async createDriverVehicle(
    @Body() dto: CreateDriverVehicleDto,
    @Req() req: Request,
  ) {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }
    const driverVehicle = await this.driverVehicleService.createDriverVehicle(
      userId,
      dto,
    );
    return {
      success: true,
      message: 'Driver vehicle created',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }
  @Post(':driverVehicleId/admin/verify-rc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and save driver vehicle document by driver vehicle ID',
    description:
      'Verify RC document and save the verified information for the driver vehicle.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle document verified and saved successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async verifyAndSaveAdmin(
    @Param('driverVehicleId') driverVehicleId: string,
    @Req() req: Request,
  ) {
    // For admin endpoints, createdBy is the admin's profile ID from token
    const adminProfileId = (req as any).user?.profileId;

    const doc =
      await this.driverVehicleDocumentService.verifyAndSaveDocument(
        driverVehicleId,
        { createdBy: adminProfileId },
      );
    return {
      success: true,
      message: 'Driver vehicle document verified and saved successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/admin/upload-noc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Upload NOC document for driver vehicle',
    description:
      'Upload or update NOC (No Objection Certificate) document for a driver vehicle. If an NOC document already exists, it will be updated with the new document URL.',
  })
  @ApiResponse({
    status: 200,
    description: 'NOC document uploaded successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found or NOC document type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async uploadNocAdmin(
    @Param('driverVehicleId') driverVehicleId: string,
    @Body() uploadNocDto: UploadNocDto,
    @Req() req: Request,
  ) {
    // For admin endpoints, createdBy is the admin's profile ID from token
    const adminProfileId = (req as any).user?.profileId;

    const doc = await this.driverVehicleDocumentService.uploadNocDocument(
      driverVehicleId,
      uploadNocDto.documentUrl,
      { createdBy: adminProfileId },
    );
    return {
      success: true,
      message: 'NOC document uploaded successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/verify-rc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and save driver vehicle document by driver vehicle ID',
    description:
      'Verify RC document and save the verified information for the driver vehicle.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle document verified and saved successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async verifyAndSave(@Param('driverVehicleId') driverVehicleId: string) {
    // For user endpoints, get the userProfileId from the driver vehicle
    const driverVehicle = await this.driverVehicleService.findDriverVehicleByIdWithRelations(driverVehicleId);
    const createdBy = driverVehicle?.userProfileId;

    const doc =
      await this.driverVehicleDocumentService.verifyAndSaveDocument(
        driverVehicleId,
        { createdBy },
      );
    return {
      success: true,
      message: 'Driver vehicle document verified and saved successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/upload-noc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Upload NOC document for driver vehicle',
    description:
      'Upload or update NOC (No Objection Certificate) document for a driver vehicle. If an NOC document already exists, it will be updated with the new document URL.',
  })
  @ApiResponse({
    status: 200,
    description: 'NOC document uploaded successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found or NOC document type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async uploadNoc(
    @Param('driverVehicleId') driverVehicleId: string,
    @Body() uploadNocDto: UploadNocDto,
  ) {
    // For user endpoints, get the userProfileId from the driver vehicle
    const driverVehicle = await this.driverVehicleService.findDriverVehicleByIdWithRelations(driverVehicleId);
    const createdBy = driverVehicle?.userProfileId;

    const doc = await this.driverVehicleDocumentService.uploadNocDocument(
      driverVehicleId,
      uploadNocDto.documentUrl,
      { createdBy },
    );
    return {
      success: true,
      message: 'NOC document uploaded successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List driver vehicles by profile ID',
    description:
      'Get all driver vehicles for a specific profile with vehicle type and city product information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicles retrieved successfully',
    type: ApiResponseDto<DriverVehicleResponseDto[]>,
  })
  @ApiQuery({
    name: 'profileId',
    required: true,
    type: String,
    description: 'User profile ID to filter driver vehicles',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - profileId query parameter is required',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getDriverVehicles(@Query('profileId') profileId: string) {
    if (!profileId) {
      throw new BadRequestException('profileId query parameter is required');
    }

    const driverVehicles =
      await this.driverVehicleService.findDriverVehiclesByUserProfileId(
        profileId,
      );

    return {
      success: true,
      message: 'Driver vehicles retrieved successfully',
      data: driverVehicles,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver vehicle by ID',
    description:
      'Get a specific driver vehicle by its ID with all related information including vehicle type, city product, and user profile.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle retrieved successfully',
    type: ApiResponseDto<DriverVehicleResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getDriverVehicleById(@Param('id') id: string) {
    const driverVehicle =
      await this.driverVehicleService.findDriverVehicleByIdWithRelations(id);

    return {
      success: true,
      message: 'Driver vehicle retrieved successfully',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Get(':id/documents')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List vehicle documents for a driver vehicle',
    description:
      'Get all vehicle documents with their associated driver vehicle documents for a specific driver vehicle ID. Document URLs are returned as signed S3 URLs for secure access.',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle documents retrieved successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getVehicleDocuments(@Param('id') driverVehicleId: string) {
    const documents =
      await this.driverVehicleDocumentService.getVehicleDocumentsWithDriverDocs(
        driverVehicleId,
      );

    return {
      success: true,
      message: 'Vehicle documents retrieved successfully',
      data: documents,
      timestamp: Date.now(),
    };
  }

  @Patch('admin/:id/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Change driver vehicle status (Admin only)',
    description:
      'Change driver vehicle status. When setting to verified, validates that all mandatory documents are approved.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Driver vehicle status changed successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Validation failed - Cannot verify vehicle due to missing approved documents',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  async changeDriverVehicleStatus(
    @Param('id') id: string,
    @Body() changeStatusDto: ChangeDriverVehicleStatusDto,
  ) {
    const result = await this.driverVehicleService.changeDriverVehicleStatus(
      id,
      changeStatusDto.status as any,
    );

    return {
      success: true,
      message: result.message,
      data: result,
      timestamp: Date.now(),
    };
  }
}
