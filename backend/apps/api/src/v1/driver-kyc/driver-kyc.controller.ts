import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  BadRequestException,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  Api<PERSON>earerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { DriverKycService } from '@shared/shared/modules/driver-kyc/driver-kyc.service';
import { CreateDriverKycDto } from './dto/create-driver-kyc.dto';
import { UpdateDriverKycDto } from './dto/update-driver-kyc.dto';
import { RejectDriverKycDto } from './dto/reject-driver-kyc.dto';
import { DriverKycResponseDto } from './dto/driver-kyc-response.dto';
import { KycDocumentWithDriverKycResponseDto } from './dto/kyc-document-with-driver-kyc-response.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { KycStatus } from '../../../../../libs/shared/src/repositories/models/driverKyc.model';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Driver KYC')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('driver-kyc')
export class DriverKycController {
  constructor(private readonly driverKycService: DriverKycService) {}

  // ==================== ADMIN ENDPOINTS ====================

  @Post('admin/add-kyc-document')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add KYC document for driver (Admin)',
    description: 'Admin endpoint to add KYC document for a driver profile',
  })
  @ApiResponse({
    status: 201,
    description: 'KYC document added successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document type or user profile not found',
    type: ApiErrorResponseDto,
  })
  async addKycDocumentForAdmin(
    @Body() createDriverKycDto: CreateDriverKycDto,
    @Req() req: Request,
  ) {
    // For admin endpoints, createdBy is the admin's profile ID from token
    const adminProfileId = (req as any).user?.profileId;

    const kycData = {
      ...createDriverKycDto,
      createdBy: adminProfileId,
      status: KycStatus.PENDING,
      expiryDate: createDriverKycDto.expiryDate
        ? new Date(createDriverKycDto.expiryDate)
        : null,
      fromDigilocker: createDriverKycDto.fromDigilocker || false,
    };

    const driverKyc =
      await this.driverKycService.createDriverKycForAdmin(kycData);
    return {
      success: true,
      message: 'KYC document added successfully',
      data: driverKyc,
      timestamp: Date.now(),
    };
  }

  @Patch('admin/edit-kyc-document/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Edit KYC document (Admin)',
    description: 'Admin endpoint to edit/update KYC document',
  })
  @ApiResponse({
    status: 200,
    description: 'KYC document updated successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document not found',
    type: ApiErrorResponseDto,
  })
  async editKycDocumentForAdmin(
    @Param('id') id: string,
    @Body() updateDriverKycDto: UpdateDriverKycDto,
  ) {
    const updateData: any = { ...updateDriverKycDto };
    if (updateDriverKycDto.expiryDate) {
      updateData.expiryDate = new Date(updateDriverKycDto.expiryDate);
    }

    const driverKyc = await this.driverKycService.updateDriverKyc(
      id,
      updateData,
    );
    return {
      success: true,
      message: 'KYC document updated successfully',
      data: driverKyc,
      timestamp: Date.now(),
    };
  }

  @Get('admin/list-documents/user-profile/:profileId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List KYC documents by profile ID (Admin)',
    description:
      'Admin endpoint to get all KYC documents for a specific driver profile',
  })
  @ApiResponse({
    status: 200,
    description: 'KYC documents retrieved successfully',
    type: ApiResponseDto<DriverKycResponseDto[]>,
  })
  @ApiResponse({
    status: 404,
    description: 'Profile not found',
    type: ApiErrorResponseDto,
  })
  async listKycDocumentsForAdmin(@Param('profileId') profileId: string) {
    const documents =
      await this.driverKycService.getKycDocumentsWithDriverKycByProfile(
        profileId,
      );
    return {
      success: true,
      message: 'KYC documents retrieved successfully',
      data: documents,
      timestamp: Date.now(),
    };
  }

  // ==================== REGULAR ENDPOINTS ====================

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Add a new driver KYC document' })
  @ApiResponse({
    status: 201,
    description: 'Driver KYC document created successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid input data or driver already has a KYC entry for this document type',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC document type or user profile not found',
    type: ApiErrorResponseDto,
  })
  async addDriverKyc(@Body() createDriverKycDto: CreateDriverKycDto) {
    // For user endpoints, createdBy is the userProfileId from the DTO (self-created)
    const createdBy = createDriverKycDto.userProfileId;

    const driverKyc = await this.driverKycService.createDriverKyc({
      ...createDriverKycDto,
      createdBy,
      status: KycStatus.PENDING,
      expiryDate: createDriverKycDto.expiryDate
        ? new Date(createDriverKycDto.expiryDate)
        : null,
      fromDigilocker: createDriverKycDto.fromDigilocker || false,
    });
    return {
      success: true,
      message: 'Driver KYC document added successfully',
      data: driverKyc,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update driver KYC document' })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC document updated successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver KYC document not found',
    type: ApiErrorResponseDto,
  })
  async updateDriverKyc(
    @Param('id') id: string,
    @Body() updateDriverKycDto: UpdateDriverKycDto,
  ) {
    const updateData: any = { ...updateDriverKycDto };
    if (updateDriverKycDto.expiryDate) {
      updateData.expiryDate = new Date(updateDriverKycDto.expiryDate);
    }

    const driverKyc = await this.driverKycService.updateDriverKyc(
      id,
      updateData,
    );
    return {
      success: true,
      message: 'Driver KYC document updated successfully',
      data: driverKyc,
    };
  }

  @Patch(':id/accept')
  @ApiOperation({ summary: 'Accept/approve driver KYC document' })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC document approved successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver KYC document not found',
    type: ApiErrorResponseDto,
  })
  async acceptDriverKyc(@Param('id') id: string) {
    const driverKyc = await this.driverKycService.approveDriverKyc(id);
    return {
      success: true,
      message: 'Driver KYC document approved successfully',
      data: driverKyc,
    };
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject driver KYC document with rejection note' })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC document rejected successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Rejection note is required',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver KYC document not found',
    type: ApiErrorResponseDto,
  })
  async rejectDriverKyc(
    @Param('id') id: string,
    @Body() rejectDriverKycDto: RejectDriverKycDto,
  ) {
    const driverKyc = await this.driverKycService.rejectDriverKyc(
      id,
      rejectDriverKycDto.rejectionNote,
    );
    return {
      success: true,
      message: 'Driver KYC document rejected successfully',
      data: driverKyc,
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated driver KYC documents with details' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'userProfileId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: KycStatus })
  @ApiQuery({ name: 'kycDocumentId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC documents retrieved successfully',
    type: PaginatedResponseDto<DriverKycResponseDto>,
  })
  async getDriverKycs(
    @Query() paginationDto: PaginationDto,
    @Query('userProfileId') userProfileId?: string,
    @Query('status') status?: KycStatus,
    @Query('kycDocumentId') kycDocumentId?: string,
  ) {
    const filters: any = {};
    if (userProfileId) filters.userProfileId = userProfileId;
    if (status) filters.status = status;
    if (kycDocumentId) filters.kycDocumentId = kycDocumentId;

    const result = await this.driverKycService.paginateDriverKycs(
      paginationDto.page || 1,
      paginationDto.limit || 10,
      filters,
    );

    return {
      success: true,
      message: 'Driver KYC documents retrieved successfully',
      data: result.data,
      meta: result.meta,
    };
  }

  @Get('documents-by-profile')
  @ApiOperation({
    summary:
      'Get all KYC documents with driver KYC data for a specific profile',
    description:
      'Returns all KYC documents with the first related driver KYC entry for the specified profile. Document URLs are returned as signed S3 URLs.',
  })
  @ApiQuery({
    name: 'profileId',
    required: true,
    type: String,
    description: 'User profile ID to filter driver KYC documents',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'KYC documents with driver KYC data retrieved successfully',
    type: ApiResponseDto<KycDocumentWithDriverKycResponseDto[]>,
  })
  @ApiResponse({
    status: 404,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  async getKycDocumentsByProfile(@Query('profileId') profileId: string) {
    if (!profileId) {
      throw new BadRequestException('profileId query parameter is required');
    }

    const data =
      await this.driverKycService.getKycDocumentsWithDriverKycByProfile(
        profileId,
      );
    return {
      success: true,
      message: 'KYC documents with driver KYC data retrieved successfully',
      data,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get driver KYC document by ID' })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC document retrieved successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver KYC document not found',
    type: ApiErrorResponseDto,
  })
  async getDriverKycById(@Param('id') id: string) {
    const driverKyc = await this.driverKycService.findDriverKycById(id);
    return {
      success: true,
      message: 'Driver KYC document retrieved successfully',
      data: driverKyc,
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete driver KYC document' })
  @ApiResponse({
    status: 200,
    description: 'Driver KYC document deleted successfully',
    type: ApiResponseDto<DriverKycResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver KYC document not found',
    type: ApiErrorResponseDto,
  })
  async deleteDriverKyc(@Param('id') id: string) {
    const driverKyc = await this.driverKycService.deleteDriverKyc(id);
    return {
      success: true,
      message: 'Driver KYC document deleted successfully',
      data: driverKyc,
    };
  }
}
