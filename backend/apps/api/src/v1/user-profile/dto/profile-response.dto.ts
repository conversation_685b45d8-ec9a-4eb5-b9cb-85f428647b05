import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';

export class ProfileResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Profile ID',
  })
  id!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'User ID',
  })
  userId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Role ID',
  })
  roleId!: string;

  @ApiPropertyOptional({
    example: 'John',
    description: 'First name',
  })
  firstName?: string;

  @ApiPropertyOptional({
    example: 'Doe',
    description: 'Last name',
  })
  lastName?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'City ID',
  })
  cityId?: string;

  @ApiPropertyOptional({
    example: 'ABC123',
    description: 'Referral code',
  })
  referralCode?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL',
  })
  profilePictureUrl?: string;

  @ApiPropertyOptional({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Language ID',
  })
  languageId?: string;

  @ApiPropertyOptional({
    enum: Gender,
    description: 'Gender',
    example: Gender.MALE,
  })
  gender?: Gender;

  @ApiPropertyOptional({
    example: '1990-01-01T00:00:00.000Z',
    description: 'Date of birth',
  })
  dob?: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00.000Z',
    description: 'Created at timestamp',
  })
  createdAt!: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00.000Z',
    description: 'Updated at timestamp',
  })
  updatedAt!: Date;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  email?: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number',
  })
  phone?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether the email is verified',
  })
  emailVerified?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether the phone is verified',
  })
  phoneVerified?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether the terms and conditions are accepted',
  })
  isPolicyAllowed?: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the user is currently online',
  })
  isOnline!: boolean;
}
