image: atlassian/default-image:4

definitions:
  caches:
    pnpm: ~/.local/share/pnpm/store

pipelines:
  branches:
    staging-server:
      - parallel:
          - step:
              name: Build and Push API to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e
                - echo "🚀 Building and pushing API service for STAGING environment"

                # Install and configure AWS CLI
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }

                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi

                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1

                # Test AWS connectivity and login to ECR
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }

                # Build and tag API image
                - docker build -f docker/Dockerfile.api -t api:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build API image"; exit 1; }
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:$BITBUCKET_BUILD_NUMBER
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:latest
                - docker tag api:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:staging

                # Push API image
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push API image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:latest || { echo "Failed to push API image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:staging || { echo "Failed to push API image with staging tag"; exit 1; }

                - echo "✅ API service pushed successfully to STAGING"

          - step:
              name: Build and Push Kafka Consumer to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e
                - echo "🚀 Building and pushing Kafka Consumer service for STAGING environment"

                # Install and configure AWS CLI
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }

                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi

                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1

                # Test AWS connectivity and login to ECR
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }

                # Build and tag Kafka Consumer image
                - docker build -f docker/Dockerfile.kafka-consumer -t kafka-consumer:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build Kafka Consumer image"; exit 1; }
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:$BITBUCKET_BUILD_NUMBER
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:latest
                - docker tag kafka-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:staging

                # Push Kafka Consumer image
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push Kafka Consumer image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:latest || { echo "Failed to push Kafka Consumer image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/kafka-consumer:staging || { echo "Failed to push Kafka Consumer image with staging tag"; exit 1; }

                - echo "✅ Kafka Consumer service pushed successfully to STAGING"

          - step:
              name: Build and Push RabbitMQ Consumer to ECR (Staging)
              services:
                - docker
              caches:
                - docker
                - pnpm
              script:
                - set -e
                - echo "🚀 Building and pushing RabbitMQ Consumer service for STAGING environment"

                # Install and configure AWS CLI
                - apt-get update && apt-get install -y python3-pip curl unzip || { echo "Failed to install dependencies"; exit 1; }
                - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }

                # Verify required environment variables
                - |
                  if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                    echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                    exit 1
                  fi

                - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
                - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
                - aws configure set default.region ap-south-1

                # Test AWS connectivity and login to ECR
                - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }
                - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 111311033809.dkr.ecr.ap-south-1.amazonaws.com || { echo "Failed to login to ECR"; exit 1; }

                # Build and tag RabbitMQ Consumer image
                - docker build -f docker/Dockerfile.rabbitmq-consumer -t rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER . || { echo "Failed to build RabbitMQ Consumer image"; exit 1; }
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:latest
                - docker tag rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:staging

                # Push RabbitMQ Consumer image
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:$BITBUCKET_BUILD_NUMBER || { echo "Failed to push RabbitMQ Consumer image with build number"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:latest || { echo "Failed to push RabbitMQ Consumer image with latest tag"; exit 1; }
                - docker push 111311033809.dkr.ecr.ap-south-1.amazonaws.com/rabbitmq-consumer:staging || { echo "Failed to push RabbitMQ Consumer image with staging tag"; exit 1; }

                - echo "✅ RabbitMQ Consumer service pushed successfully to STAGING"

      - step:
          name: Deploy to ECS and Run Migrations (Staging)
          script:
            - set -e
            - echo "🚀 Deploying services to ECS and running migrations for STAGING environment"

            # Verify task definitions directory exists
            - ls -la aws/task-definitions/ || { echo "AWS task definitions directory not found"; exit 1; }

            # Install and configure AWS CLI
            - apt-get update && apt-get install -y python3-pip curl unzip jq || { echo "Failed to install dependencies"; exit 1; }
            - pip3 install awscli || { echo "Failed to install AWS CLI"; exit 1; }

            # Verify required environment variables
            - |
              if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
                echo "Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in repository variables."
                exit 1
              fi
              if [ -z "$ECS_CLUSTER_NAME" ]; then
                echo "Error: ECS cluster name not set. Please set ECS_CLUSTER_NAME in repository variables."
                exit 1
              fi
              echo "Using ECS Cluster: $ECS_CLUSTER_NAME"

            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set default.region ap-south-1

            # Test AWS connectivity
            - aws sts get-caller-identity || { echo "Failed to authenticate with AWS"; exit 1; }

            # Verify template files exist and update task definitions
            - |
              for template in api kafka-consumer rabbitmq-consumer migration; do
                if [ ! -f "aws/task-definitions/${template}-task-definition-template.json" ]; then
                  echo "❌ Template file not found: aws/task-definitions/${template}-task-definition-template.json"
                  exit 1
                fi
              done
              echo "✅ All template files found"

            # Update and register task definitions
            - sed "s/BUILD_NUMBER_PLACEHOLDER/$BITBUCKET_BUILD_NUMBER/g" aws/task-definitions/api-task-definition-template.json > api-task-def-updated.json
            - aws ecs register-task-definition --cli-input-json file://api-task-def-updated.json || { echo "Failed to register API task definition"; cat api-task-def-updated.json; exit 1; }

            - sed "s/BUILD_NUMBER_PLACEHOLDER/$BITBUCKET_BUILD_NUMBER/g" aws/task-definitions/kafka-consumer-task-definition-template.json > kafka-consumer-task-def-updated.json
            - aws ecs register-task-definition --cli-input-json file://kafka-consumer-task-def-updated.json || { echo "Failed to register Kafka Consumer task definition"; cat kafka-consumer-task-def-updated.json; exit 1; }

            - sed "s/BUILD_NUMBER_PLACEHOLDER/$BITBUCKET_BUILD_NUMBER/g" aws/task-definitions/rabbitmq-consumer-task-definition-template.json > rabbitmq-consumer-task-def-updated.json
            - aws ecs register-task-definition --cli-input-json file://rabbitmq-consumer-task-def-updated.json || { echo "Failed to register RabbitMQ Consumer task definition"; cat rabbitmq-consumer-task-def-updated.json; exit 1; }

            - sed "s/BUILD_NUMBER_PLACEHOLDER/$BITBUCKET_BUILD_NUMBER/g" aws/task-definitions/migration-task-definition-template.json > migration-task-def-updated.json
            - aws ecs register-task-definition --cli-input-json file://migration-task-def-updated.json || { echo "Failed to register Migration task definition"; cat migration-task-def-updated.json; exit 1; }

            # Deploy services to ECS
            - |
              # Deploy API service
              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services api-service-5b8iycmw &>/dev/null; then
                aws ecs update-service \
                  --cluster $ECS_CLUSTER_NAME \
                  --service api-service-5b8iycmw \
                  --task-definition api || { echo "Failed to update API service"; exit 1; }
                echo "✅ API service updated successfully"
              else
                echo "ℹ️ API service does not exist, skipping update"
              fi

              # Deploy Kafka Consumer service
              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services kafka-consumer-service-ptlsvr1e &>/dev/null; then
                aws ecs update-service \
                  --cluster $ECS_CLUSTER_NAME \
                  --service kafka-consumer-service-ptlsvr1e \
                  --task-definition kafka-consumer || { echo "Failed to update Kafka Consumer service"; exit 1; }
                echo "✅ Kafka Consumer service updated successfully"
              else
                echo "ℹ️ Kafka Consumer service does not exist, skipping update"
              fi

              # Deploy RabbitMQ Consumer service
              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services rabbitmq-consumer-service-k9utbonb &>/dev/null; then
                aws ecs update-service \
                  --cluster $ECS_CLUSTER_NAME \
                  --service rabbitmq-consumer-service-k9utbonb \
                  --task-definition rabbitmq-consumer || { echo "Failed to update RabbitMQ Consumer service"; exit 1; }
                echo "✅ RabbitMQ Consumer service updated successfully"
              else
                echo "ℹ️ RabbitMQ Consumer service does not exist, skipping update"
              fi

            # Wait for deployments to complete
            - |
              SERVICES_TO_WAIT=()

              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services api-service-5b8iycmw &>/dev/null; then
                SERVICES_TO_WAIT+=("api-service-5b8iycmw")
              fi

              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services kafka-consumer-service-ptlsvr1e &>/dev/null; then
                SERVICES_TO_WAIT+=("kafka-consumer-service-ptlsvr1e")
              fi

              if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services rabbitmq-consumer-service-k9utbonb &>/dev/null; then
                SERVICES_TO_WAIT+=("rabbitmq-consumer-service-k9utbonb")
              fi

              if [ ${#SERVICES_TO_WAIT[@]} -gt 0 ]; then
                echo "Waiting for services to reach steady state: ${SERVICES_TO_WAIT[*]}"
                aws ecs wait services-stable --cluster $ECS_CLUSTER_NAME --services ${SERVICES_TO_WAIT[*]} || { echo "Some services failed to reach stable state"; exit 1; }
                echo "✅ All services reached steady state successfully!"
              else
                echo "ℹ️ No services to wait for"
              fi


            # Run database migrations and seeding AFTER service deployment
            - |
              MIGRATION_TASK_ARN=$(aws ecs run-task \
                --cluster $ECS_CLUSTER_NAME \
                --task-definition api-migration \
                --query 'tasks[0].taskArn' \
                --output text) || { echo "Failed to start migration task"; exit 1; }

              echo "Migration task started: $MIGRATION_TASK_ARN"

              aws ecs wait tasks-stopped \
                --cluster $ECS_CLUSTER_NAME \
                --tasks $MIGRATION_TASK_ARN || { echo "Migration task failed or timeout"; exit 1; }

              # Check if migration task succeeded
              EXIT_CODE=$(aws ecs describe-tasks \
                --cluster $ECS_CLUSTER_NAME \
                --tasks $MIGRATION_TASK_ARN \
                --query 'tasks[0].containers[0].exitCode' \
                --output text)

              if [ "$EXIT_CODE" != "0" ]; then
                echo "❌ Migration task failed with exit code: $EXIT_CODE"
                aws logs get-log-events --log-group-name "/ecs/api-migration" --log-stream-name "ecs/migration-container/$(echo $MIGRATION_TASK_ARN | cut -d'/' -f3)" --limit 50
                exit 1
              fi

              echo "✅ Database migrations and seeding completed successfully!"

            - echo "✅ Deployment completed successfully!"
