{"family": "api-migration", "executionRoleArn": "arn:aws:iam::111311033809:role/ecsTaskExecutionRole", "networkMode": "bridge", "requiresCompatibilities": ["EC2"], "cpu": "256", "memory": "512", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "containerDefinitions": [{"name": "migration-container", "image": "111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:BUILD_NUMBER_PLACEHOLDER", "cpu": 0, "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::tuxi-staging-bucket/server-variables/.env", "type": "s3"}], "command": ["/bin/sh", "-c", "echo 'Starting database migrations...' && cd /app && pnpx prisma migrate deploy && echo 'Migrations completed. Starting database seeding...' && pnpm run prisma:seed:prod && echo 'Database seeding completed successfully!'"], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/api-migration", "awslogs-create-group": "true", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}]}