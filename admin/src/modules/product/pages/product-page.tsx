'use client';

import { useState } from 'react';
import { useListProduct } from '../api/queries';
import { ProductModal } from '../components/product-modal';
import { ProductTable } from '../components/product-table';

export function ProductPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const listProductQuery = useListProduct({
    page,
    limit,
  });

  return (
    <div className='flex flex-1 flex-col gap-4 p-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-2xl font-semibold text-gray-900'>Products</h2>
        <ProductModal mode="create" />
      </div>

      <ProductTable
        data={listProductQuery.data}
        isLoading={listProductQuery.isLoading}
        currentPage={page}
        onPageChange={(newPage: number) => setPage(newPage)}
      />
    </div>
  );
}
