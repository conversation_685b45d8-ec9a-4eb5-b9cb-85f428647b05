import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  ProductResponse,
  ListProductParams,
  ListProductResponse,
  DropdownOption,
} from '../types/product';

export const useListProduct = ({
  page = 1,
  limit = 10,
  search,
  sortBy,
  sortOrder,
}: ListProductParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: [
      'products',
      page,
      limit,
      search,
      sortBy,
      sortOrder,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListProductResponse> => {
      return apiClient.get('/products', {
        params: {
          page,
          limit,
          search,
          sortBy,
          sortOrder,
        },
      });
    },
  });
};

export const useGetProduct = (id: string | null) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: (): Promise<ProductResponse> => {
      return apiClient.get(`/products/${id || ''}`);
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};

// Query for vehicle types dropdown
export const useListVehicleTypes = () => {
  return useQuery({
    queryKey: ['vehicle-types-dropdown'],
    queryFn: (): Promise<{ data: DropdownOption[] }> => {
      return apiClient.get('/vehicle-type');
    },
    refetchOnWindowFocus: false,
  });
};

// Query for product services dropdown
export const useListProductServices = () => {
  return useQuery({
    queryKey: ['product-services-dropdown'],
    queryFn: (): Promise<{ data: DropdownOption[] }> => {
      return apiClient.get('/product-services');
    },
    refetchOnWindowFocus: false,
  });
};
