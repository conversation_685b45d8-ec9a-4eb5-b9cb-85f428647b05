// City-related TypeScript interfaces based on backend DTOs

export interface City {
  id: string;
  name: string;
  icon?: string | null;
  state?: string | null;
  country?: string | null;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface LatLng {
  lat: number;
  lng: number;
}

export interface CreateCityRequest {
  name: string;
  icon?: string;
  state?: string;
  country?: string;
  polygon?: LatLng[];
  status?: 'active' | 'inactive';
}

export interface UpdateCityRequest {
  name?: string;
  icon?: string;
  state?: string;
  country?: string;
  polygon?: LatLng[];
  status?: 'active' | 'inactive';
}

export interface ChangeCityStatusRequest {
  status: 'active' | 'inactive';
}

// API Response Types
export interface CityResponse {
  success: boolean;
  message: string;
  data: City;
  timestamp: number;
}

export interface ListCityParams {
  page?: number;
  limit?: number;
  search?: string;
  state?: string;
  status?: string;
}

export interface ListCityResponse {
  success: boolean;
  message: string;
  data: City[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  timestamp: number;
}

export interface CityCreateResponse {
  success: boolean;
  message: string;
  data: City;
  timestamp: number;
}

export interface CityUpdateResponse {
  success: boolean;
  message: string;
  data: City;
  timestamp: number;
}

export interface CityStatusChangeResponse {
  success: boolean;
  message: string;
  data: City;
  timestamp: number;
}