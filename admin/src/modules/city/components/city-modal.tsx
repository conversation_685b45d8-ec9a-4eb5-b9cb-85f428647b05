'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateCity, useUpdateCity } from '../api/mutations';
import { useGetCity } from '../api/queries';

const citySchema = z.object({
  name: z
    .string()
    .min(1, 'City name is required')
    .min(2, 'City name must be at least 2 characters')
    .max(100, 'City name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s.'-]+$/, 'City name can only contain letters, spaces, dots, apostrophes, and hyphens'),
  state: z
    .string()
    .optional(),
  country: z
    .string()
    .optional(),
  status: z
    .enum(['active', 'inactive']),
});

type CityFormValues = z.infer<typeof citySchema>;

interface CityModalProps {
  cityId?: string | null;
  isOpen?: boolean;
  onClose?: () => void;
  onCityUpdated?: () => void;
  mode?: 'create' | 'edit';
}

export const CityModal = ({ 
  cityId, 
  isOpen, 
  onClose, 
  onCityUpdated,
  mode = 'create' 
}: CityModalProps) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const createCityMutation = useCreateCity();
  const updateCityMutation = useUpdateCity();
  const cityQuery = useGetCity(cityId || null);
  const queryClient = useQueryClient();

  // Determine if this is edit mode
  const isEditMode = mode === 'edit' || !!cityId;
  
  // Use external open state if provided, otherwise use internal state
  const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
  const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

  const form = useForm<CityFormValues>({
    resolver: zodResolver(citySchema),
    defaultValues: {
      name: '',
      state: '',
      country: '',
      status: 'active',
    },
  });

  const {
    formState: { errors },
    reset,
    control,
    handleSubmit,
    setValue,
  } = form;

  // Pre-populate form when city data is loaded for edit mode
  useEffect(() => {
    if (isEditMode && cityQuery.data?.data) {
      const city = cityQuery.data.data;
      setValue('name', city.name);
      setValue('state', city.state || '');
      setValue('country', city.country || '');
      setValue('status', city.status);
    } else if (!isEditMode) {
      // Reset form for create mode
      reset({
        name: '',
        state: '',
        country: '',
        status: 'active',
      });
    }
  }, [isEditMode, cityQuery.data, setValue, reset]);

  const onSubmit = async (data: CityFormValues) => {
    try {
      if (isEditMode && cityId) {
        // Update existing city
        await updateCityMutation.mutateAsync({
          id: cityId,
          name: data.name,
          state: data.state || undefined,
          country: data.country || undefined,
          status: data.status,
        });
        toast.success('City updated successfully');
        queryClient.invalidateQueries({ queryKey: ['city', cityId] });
        onCityUpdated?.();
      } else {
        // Create new city
        await createCityMutation.mutateAsync({
          name: data.name,
          state: data.state || undefined,
          country: data.country || undefined,
          status: data.status,
        });
        toast.success('City created successfully');
      }

      queryClient.invalidateQueries({ queryKey: ['cities'] });
      reset();
      setModalOpen(false);
    } catch (error: any) {
      toast.error(error?.message || `Failed to ${isEditMode ? 'update' : 'create'} city`);
    }
  };

  const handleClose = () => {
    reset();
    setModalOpen(false);
  };

  const isLoading = isEditMode ? cityQuery.isLoading : false;
  const isError = isEditMode ? (cityQuery.isError || !cityQuery.data?.data) : false;
  const mutation = isEditMode ? updateCityMutation : createCityMutation;

  const TriggerButton = () => (
    <Button onClick={() => setModalOpen(true)} className='flex items-center gap-2'>
      <Plus className='w-4 h-4' />
      Add City
    </Button>
  );

  // Show loading state for edit mode
  if (isLoading) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='sm:max-w-md'>
          <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state for edit mode
  if (isError) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>
              Failed to load city data. Please try again.
            </DialogDescription>
          </DialogHeader>
          <div className='flex justify-end pt-4'>
            <Button variant='outline' onClick={handleClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      {!isOpen && !isEditMode && (
        <DialogTrigger asChild>
          <TriggerButton />
        </DialogTrigger>
      )}

      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit City' : 'Create New City'}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Update the city information below.'
              : 'Add a new city to the platform. Fill in the details below.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
          {/* City Name */}
          <div className='space-y-2'>
            <Label htmlFor='name'>City Name *</Label>
            <Controller
              name='name'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='name'
                  placeholder='Enter city name'
                  disabled={mutation.isPending}
                />
              )}
            />
            <ErrorMessage error={errors.name?.message} />
          </div>

          {/* State */}
          <div className='space-y-2'>
            <Label htmlFor='state'>State</Label>
            <Controller
              name='state'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='state'
                  placeholder='Enter state name'
                  disabled={mutation.isPending}
                />
              )}
            />
            <ErrorMessage error={errors.state?.message} />
          </div>

          {/* Country */}
          <div className='space-y-2'>
            <Label htmlFor='country'>Country</Label>
            <Controller
              name='country'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='country'
                  placeholder='Enter country name'
                  disabled={mutation.isPending}
                />
              )}
            />
            <ErrorMessage error={errors.country?.message} />
          </div>

          {/* Status */}
          <div className='space-y-2'>
            <Label htmlFor='status'>Status *</Label>
            <Controller
              name='status'
              control={control}
              render={({ field }) => (
                <Select 
                  value={field.value} 
                  onValueChange={field.onChange}
                  disabled={mutation.isPending}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='active'>Active</SelectItem>
                    <SelectItem value='inactive'>Inactive</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            <ErrorMessage error={errors.status?.message} />
          </div>

          {/* Form Actions */}
          <div className='flex justify-end gap-3 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={mutation.isPending}>
              {mutation.isPending ? (
                <>
                  <Spinner className='h-4 w-4 mr-2' />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update City' : 'Create City'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};