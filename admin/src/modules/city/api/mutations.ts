import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  CreateCityRequest,
  UpdateCityRequest,
  ChangeCityStatusRequest,
  CityCreateResponse,
  CityUpdateResponse,
  CityStatusChangeResponse,
} from '../types/city';

/**
 * Hook for creating a new city
 */
export const useCreateCity = () => {
  return useMutation({
    mutationFn: async (data: CreateCityRequest): Promise<CityCreateResponse> => {
      return apiClient.post('/cities', data);
    },
  });
};

/**
 * Hook for updating a city
 */
export const useUpdateCity = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & UpdateCityRequest): Promise<CityUpdateResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/cities/${id}`, payload);
    },
  });
};

/**
 * Hook for updating city status (active/inactive)
 */
export const useUpdateCityStatus = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & ChangeCityStatusRequest): Promise<CityStatusChangeResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/cities/${id}/status`, payload);
    },
  });
};