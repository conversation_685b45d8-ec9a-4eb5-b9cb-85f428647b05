import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  City,
  CityResponse,
  ListCityParams,
  ListCityResponse,
} from '../types/city';

export const useListCities = ({
  page = 1,
  limit = 10,
  search,
  state,
  status,
}: ListCityParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: [
      'cities',
      page,
      limit,
      search,
      state,
      status,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListCityResponse> => {
      return apiClient.get('/cities/admin/paginate', {
        params: {
          page,
          limit,
          search,
          state,
          status,
        },
      });
    },
  });
};

export const useGetCity = (id: string | null) => {
  return useQuery({
    queryKey: ['city', id],
    queryFn: (): Promise<CityResponse> => {
      return apiClient.get(`/cities/${id || ''}`);
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for fetching all cities (for dropdown usage)
 */
export const useAllCities = () => {
  return useQuery({
    queryKey: ['cities-all'],
    queryFn: (): Promise<{
      success: boolean;
      message: string;
      data: City[];
      timestamp: number;
    }> => {
      return apiClient.get('/cities');
    },
    staleTime: 5 * 60 * 1000, // 5 minutes - cities don't change often
    refetchOnWindowFocus: false,
  });
};