export const validateAadhaarNumber = (aadhaarNumber: string): boolean => {
   if (!aadhaarNumber || typeof aadhaarNumber !== 'string') {
      return false;
   }

   const cleanAadhaar = aadhaarNumber.replace(/\s/g, '');

   if (cleanAadhaar.length !== 12) {
      return false;
   }

   if (!/^\d{12}$/.test(cleanAadhaar)) {
      return false;
   }

   return true;
};

export const validateDrivingLicenseNumber = (licenseNumber: string): boolean => {
   if (!licenseNumber || typeof licenseNumber !== 'string') {
      return false;
   }

   const cleanLicense = licenseNumber.replace(/\s/g, '').toUpperCase();

   const patterns = [
      /^[A-Z]{2}[0-9]{2}[0-9]{4}[0-9]{7}$/,
      /^[A-Z]{2}-?[0-9]{13}$/,
      /^[A-Z]{2}[0-9]{13}$/,
      /^[A-Z]{3}[0-9]{10}$/,
      /^[A-Z]{2}[0-9]{2}[0-9]{11}$/,
   ];

   const isValidPattern = patterns.some(pattern => pattern.test(cleanLicense));
   
   if (!isValidPattern) {
      return false;
   }

   const stateCodesIndia = [
      'AN', 'AP', 'AR', 'AS', 'BR', 'CG', 'CH', 'DN', 'DD', 'DL', 'GA', 'GJ', 'HR', 'HP', 'JK', 
      'JH', 'KA', 'KL', 'LD', 'MP', 'MH', 'MN', 'ML', 'MZ', 'NL', 'OR', 'PY', 'PB', 'RJ', 'SK', 
      'TN', 'TS', 'TR', 'UP', 'UK', 'WB'
   ];

   const stateCode = cleanLicense.substring(0, 2);
   
   if (cleanLicense.startsWith('DL')) {
      return true;
   }
   
   return stateCodesIndia.includes(stateCode);
};

export const formatAadhaarNumber = (value: string): string => {
   const cleaned = value.replace(/\D/g, '');
   const limited = cleaned.substring(0, 12);
   return limited.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3').trim();
};

export const formatDrivingLicenseNumber = (value: string): string => {
   return value.toUpperCase().replace(/[^A-Z0-9]/g, '');
};