'use client';

import { FileUploadSection } from '@/components/file-upload-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { formatBytes, useFileUpload as useFileUploadHook } from '@/hooks/use-file-upload';
import { formatDateForDisplay } from '@/lib/date-utils';
import { useFileUpload } from '@/lib/file-upload-api';
import { useQueryClient } from '@tanstack/react-query';
import { AlertCircle, FileText } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useUploadVehicleDocument } from '../api/mutations';
import { useVehicleDocuments } from '../api/queries';
import { DriverVehicle } from '../types/driver';
import { ApproveVehicleDocumentModal } from './approve-vehicle-document-modal';
import { RejectVehicleDocumentModal } from './reject-vehicle-document-modal';

interface VehicleDocumentsProps {
   vehicle: DriverVehicle;
}

export function VehicleDocuments({ vehicle }: VehicleDocumentsProps) {
   const [approvingDocument, setApprovingDocument] = useState<{ id: string; name: string } | null>(
      null
   );
   const [rejectingDocument, setRejectingDocument] = useState<{ id: string; name: string } | null>(
      null
   );
   const [isUploading, setIsUploading] = useState(false);
   const queryClient = useQueryClient();

   const fileUploadMutation = useFileUpload();
   const uploadDocumentMutation = useUploadVehicleDocument();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.pdf,.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
      },
   ] = useFileUploadHook({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   const { data: documentsResponse, isLoading, error } = useVehicleDocuments(vehicle.id);
   const documents = documentsResponse?.data || [];

   const getStatusBadge = (status: string) => {
      switch (status) {
         case 'APPROVED':
            return (
               <Badge className='bg-green-100 text-green-700 text-xs font-medium px-2 py-1'>
                  Approved
               </Badge>
            );
         case 'REJECTED':
            return (
               <Badge className='bg-red-100 text-red-700 text-xs font-medium px-2 py-1'>
                  Rejected
               </Badge>
            );
         case 'PENDING':
         default:
            return (
               <Badge className='bg-yellow-100 text-yellow-700 text-xs font-medium px-2 py-1'>
                  Pending
               </Badge>
            );
      }
   };

   const isNocDocument = (identifier: string) => identifier === 'noc';

   const handleModalClose = () => {
      setApprovingDocument(null);
      setRejectingDocument(null);
      clearFiles();
      clearErrors();
      queryClient.invalidateQueries({ queryKey: ['vehicle-documents', vehicle.id] });
   };

   const handleFileUpload = async () => {
      if (!file) return;

      setIsUploading(true);

      fileUploadMutation.mutate(file.file as File, {
         onSuccess: uploadResponse => {
            uploadDocumentMutation.mutate(
               {
                  vehicleId: vehicle.id,
                  documentUrl: uploadResponse.data.key,
               },
               {
                  onSuccess: () => {
                     toast.success('Document uploaded successfully!');
                     clearFiles();
                     queryClient.invalidateQueries({ queryKey: ['vehicle-documents', vehicle.id] });
                  },
                  onSettled: () => {
                     setIsUploading(false);
                  },
               }
            );
         },
         onSettled: () => {
            if (!uploadDocumentMutation.isPending) {
               setIsUploading(false);
            }
         },
      });
   };

   if (isLoading) {
      return (
         <div className='space-y-4'>
            <div className='flex justify-between items-center'>
               <h4 className='text-md font-medium'>Vehicle Documents</h4>
            </div>
            <div className='space-y-3'>
               {[1, 2, 3].map(i => (
                  <Card key={i} className='p-4'>
                     <div className='animate-pulse'>
                        <div className='h-6 bg-gray-200 rounded w-1/4 mb-2'></div>
                        <div className='h-16 bg-gray-200 rounded'></div>
                     </div>
                  </Card>
               ))}
            </div>
         </div>
      );
   }

   if (error || !documentsResponse?.success) {
      return (
         <div className='space-y-4'>
            <div className='flex justify-between items-center'>
               <h4 className='text-md font-medium'>Vehicle Documents</h4>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>Failed to Load Documents</h3>
               <p className='text-gray-600'>There was an error loading the vehicle documents.</p>
            </Card>
         </div>
      );
   }

   return (
      <div className='space-y-4'>
         <div className='flex justify-between items-center'>
            <h4 className='text-md font-medium'>Vehicle Documents</h4>
            <Badge variant='outline' className='text-xs'>
               {documents.length} Documents
            </Badge>
         </div>

         {documents.length === 0 ? (
            <Card className='p-8 text-center'>
               <FileText className='w-12 h-12 text-gray-400 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>No Documents Found</h3>
               <p className='text-gray-600 mb-4'>
                  No documents are available for this vehicle yet.
               </p>
               {vehicle.isNocRequired && (
                  <div className='bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4'>
                     <div className='flex items-center justify-center gap-2 mb-2'>
                        <AlertCircle className='w-5 h-5 text-orange-600' />
                        <Badge
                           variant='outline'
                           className='border-orange-200 text-orange-700 bg-orange-50'
                        >
                           NOC Required
                        </Badge>
                     </div>
                     <p className='text-sm text-orange-700'>
                        This vehicle requires a No Objection Certificate (NOC). The NOC document
                        will be available for upload once other vehicle documents are processed.
                     </p>
                  </div>
               )}
            </Card>
         ) : (
            <div className='space-y-4'>
               {/* Show NOC Required info at the top if required, even when documents exist */}
               {vehicle.isNocRequired && !documents.some(doc => isNocDocument(doc.identifier)) && (
                  <Card className='p-4 bg-orange-50 border-orange-200'>
                     <div className='flex items-center gap-3'>
                        <div className='w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center'>
                           <AlertCircle className='w-4 h-4 text-orange-600' />
                        </div>
                        <div className='flex-1'>
                           <div className='flex items-center gap-2 mb-1'>
                              <h5 className='font-medium text-gray-900'>
                                 No Objection Certificate (NOC)
                              </h5>
                              <Badge
                                 variant='outline'
                                 className='text-xs border-orange-200 text-orange-700 bg-orange-50'
                              >
                                 NOC Required
                              </Badge>
                           </div>
                           <p className='text-sm text-orange-700'>
                              This vehicle requires NOC documentation. The NOC upload option will be
                              available once other vehicle documents are processed.
                           </p>
                        </div>
                     </div>
                  </Card>
               )}

               {documents.map(document => (
                  <Card key={document.id} className='p-4'>
                     <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-3'>
                           <div className='w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center'>
                              <FileText className='w-4 h-4 text-blue-600' />
                           </div>
                           <div>
                              <h5 className='font-medium text-gray-900'>{document.name}</h5>
                              <p className='text-sm text-gray-500 capitalize'>
                                 {document.identifier.replace('_', ' ')}
                              </p>
                           </div>
                        </div>

                        <div className='flex items-center gap-2'>
                           {document.driverDocument &&
                              getStatusBadge(document.driverDocument.status)}

                           {/* Show NOC Required badge next to status */}
                           {isNocDocument(document.identifier) && vehicle.isNocRequired && (
                              <Badge
                                 variant='outline'
                                 className='text-xs border-orange-200 text-orange-700 bg-orange-50'
                              >
                                 NOC Required
                              </Badge>
                           )}
                        </div>
                     </div>

                     {/* NOC Document - Always show upload interface */}
                     {isNocDocument(document.identifier) && vehicle.isNocRequired ? (
                        <div className='space-y-3'>
                           <FileUploadSection
                              label='NOC Document'
                              fieldName='noc'
                              maxSize={maxSize}
                              existingFileUrl={document.driverDocument?.documentUrl}
                              fileError=''
                              fileUploadState={{ files, isDragging, errors }}
                              fileUploadActions={{
                                 handleDragEnter,
                                 handleDragLeave,
                                 handleDragOver,
                                 handleDrop,
                                 openFileDialog,
                                 removeFile,
                                 getInputProps,
                                 addFiles: () => {}, // Not used in this context
                                 clearFiles,
                                 clearErrors,
                                 handleFileChange: () => {}, // Not used in this context
                              }}
                              uploadText={
                                 document.driverDocument
                                    ? 'Upload New Document'
                                    : 'Upload NOC Document'
                              }
                              uploadDescription={`Drag & drop or click to browse (max. ${formatBytes(
                                 maxSize
                              )})`}
                              supportedTypesText='Supported: JPEG, PNG, PDF'
                              requiresFile={true}
                              enableDirectSubmission={true}
                              onDirectSubmit={() => {
                                 handleFileUpload();
                              }}
                              isSubmitting={isUploading}
                              submitButtonText='Submit'
                           />

                           {/* Rejection Note */}
                           {document.driverDocument?.status === 'REJECTED' &&
                              document.driverDocument.rejectionNote && (
                                 <div className='bg-red-50 border border-red-200 rounded-lg p-3'>
                                    <div className='flex items-start gap-2'>
                                       <AlertCircle className='w-4 h-4 text-red-600 mt-0.5' />
                                       <div>
                                          <h6 className='text-sm font-medium text-red-800 mb-1'>
                                             Rejection Reason
                                          </h6>
                                          <p className='text-sm text-red-700'>
                                             {document.driverDocument.rejectionNote}
                                          </p>
                                       </div>
                                    </div>
                                 </div>
                              )}

                           {/* Action buttons based on document status */}
                           {document.driverDocument && (
                              <div className='flex items-center justify-end gap-2 pt-2 border-t border-gray-200'>
                                 {/* Pending: Show Approve & Reject */}
                                 {document.driverDocument.status === 'PENDING' && (
                                    <>
                                       <Button
                                          size='sm'
                                          variant='outline'
                                          onClick={() =>
                                             setApprovingDocument({
                                                id: document.driverDocument!.id,
                                                name: document.name,
                                             })
                                          }
                                          className='border border-green-300 bg-green-50 text-green-700 hover:text-green-800 hover:border-green-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                       >
                                          Approve
                                       </Button>
                                       <Button
                                          size='sm'
                                          variant='outline'
                                          onClick={() =>
                                             setRejectingDocument({
                                                id: document.driverDocument!.id,
                                                name: document.name,
                                             })
                                          }
                                          className='border border-red-300 bg-red-50 text-red-700 hover:text-red-800 hover:border-red-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                       >
                                          Reject
                                       </Button>
                                    </>
                                 )}

                                 {/* Approved: Show Reject */}
                                 {document.driverDocument.status === 'APPROVED' && (
                                    <Button
                                       size='sm'
                                       variant='outline'
                                       onClick={() =>
                                          setRejectingDocument({
                                             id: document.driverDocument!.id,
                                             name: document.name,
                                          })
                                       }
                                       className='border border-red-300 bg-red-50 text-red-700 hover:text-red-800 hover:border-red-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                    >
                                       Reject
                                    </Button>
                                 )}

                                 {/* Rejected: Show Approve */}
                                 {document.driverDocument.status === 'REJECTED' && (
                                    <Button
                                       size='sm'
                                       variant='outline'
                                       onClick={() =>
                                          setApprovingDocument({
                                             id: document.driverDocument!.id,
                                             name: document.name,
                                          })
                                       }
                                       className='border border-green-300 bg-green-50 text-green-700 hover:text-green-800 hover:border-green-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                    >
                                       Approve
                                    </Button>
                                 )}
                              </div>
                           )}
                        </div>
                     ) : !isNocDocument(document.identifier) && document.driverDocument ? (
                        <div className='space-y-3'>
                           {/* Document Details for non-NOC documents */}
                           <div className='bg-gray-50 rounded-lg p-3'>
                              <div className='space-y-2 text-sm'>
                                 <div>
                                    <span className='text-gray-500'>Status: </span>
                                    <span className='text-gray-900'>
                                       {document.driverDocument.status}
                                    </span>
                                 </div>
                                 <div>
                                    <span className='text-gray-500'>Created: </span>
                                    <span className='text-gray-900'>
                                       {formatDateForDisplay(document.driverDocument.createdAt)}
                                    </span>
                                 </div>
                              </div>

                              {/* Show extracted details for auto-populated documents */}
                              {document.driverDocument.details &&
                                 document.identifier === 'vehicle_registration' && (
                                    <div className='mt-3 pt-3 border-t border-gray-200'>
                                       <p className='text-sm font-medium text-gray-700 mb-2'>
                                          Vehicle Details:
                                       </p>
                                       <div className='space-y-1 text-xs'>
                                          <div>
                                             <strong>Owner:</strong>{' '}
                                             {document.driverDocument.details.owner}
                                          </div>
                                          <div>
                                             <strong>Model:</strong>{' '}
                                             {document.driverDocument.details.model}
                                          </div>
                                          <div>
                                             <strong>Engine:</strong>{' '}
                                             {document.driverDocument.details.engine}
                                          </div>
                                          <div>
                                             <strong>Chassis:</strong>{' '}
                                             {document.driverDocument.details.chassis}
                                          </div>
                                          <div>
                                             <strong>Registration:</strong>{' '}
                                             {document.driverDocument.details.reg_date}
                                          </div>
                                          <div>
                                             <strong>Status:</strong>{' '}
                                             {document.driverDocument.details.status}
                                          </div>
                                          {document.driverDocument.details.class && (
                                             <div>
                                                <strong>Class:</strong>{' '}
                                                {document.driverDocument.details.class}
                                             </div>
                                          )}
                                          {document.driverDocument.details.body_type && (
                                             <div>
                                                <strong>Body Type:</strong>{' '}
                                                {document.driverDocument.details.body_type}
                                             </div>
                                          )}
                                          {document.driverDocument.details.rc_status && (
                                             <div>
                                                <strong>RC Status:</strong>{' '}
                                                {document.driverDocument.details.rc_status}
                                             </div>
                                          )}
                                          {document.driverDocument.details.rc_expiry_date && (
                                             <div>
                                                <strong>RC Expiry:</strong>{' '}
                                                {document.driverDocument.details.rc_expiry_date}
                                             </div>
                                          )}
                                          {document.driverDocument.details.pucc_number && (
                                             <div>
                                                <strong>PUCC Number:</strong>{' '}
                                                {document.driverDocument.details.pucc_number}
                                             </div>
                                          )}
                                          {document.driverDocument.details.pucc_upto && (
                                             <div>
                                                <strong>PUCC Valid Until:</strong>{' '}
                                                {document.driverDocument.details.pucc_upto}
                                             </div>
                                          )}
                                       </div>
                                    </div>
                                 )}

                              {/* Show insurance details */}
                              {document.driverDocument.details &&
                                 document.identifier === 'insurance' && (
                                    <div className='mt-3 pt-3 border-t border-gray-200'>
                                       <p className='text-sm font-medium text-gray-700 mb-2'>
                                          Insurance Details:
                                       </p>
                                       <div className='space-y-1 text-xs'>
                                          <div>
                                             <strong>Company:</strong>{' '}
                                             {
                                                document.driverDocument.details
                                                   .vehicle_insurance_company_name
                                             }
                                          </div>
                                          <div>
                                             <strong>Policy:</strong>{' '}
                                             {
                                                document.driverDocument.details
                                                   .vehicle_insurance_policy_number
                                             }
                                          </div>
                                          <div>
                                             <strong>Valid Until:</strong>{' '}
                                             {
                                                document.driverDocument.details
                                                   .vehicle_insurance_upto
                                             }
                                          </div>
                                       </div>
                                    </div>
                                 )}
                           </div>

                           {/* Rejection Note - Show first */}
                           {document.driverDocument.status === 'REJECTED' &&
                              document.driverDocument.rejectionNote && (
                                 <div className='bg-red-50 border border-red-200 rounded-lg p-3'>
                                    <div className='flex items-start gap-2'>
                                       <AlertCircle className='w-4 h-4 text-red-600 mt-0.5' />
                                       <div>
                                          <h6 className='text-sm font-medium text-red-800 mb-1'>
                                             Rejection Reason
                                          </h6>
                                          <p className='text-sm text-red-700'>
                                             {document.driverDocument.rejectionNote}
                                          </p>
                                       </div>
                                    </div>
                                 </div>
                              )}

                           {/* Action Buttons for non-NOC documents - Show after rejection note */}
                           <div className='space-y-2 pt-2 border-t border-gray-200'>
                              <div className='flex items-center gap-2 flex-wrap'>
                                 {document.driverDocument.documentUrl && (
                                    <Button
                                       size='sm'
                                       variant='outline'
                                       onClick={() => {
                                          if (typeof window !== 'undefined') {
                                             window.open(
                                                document.driverDocument!.documentUrl!,
                                                '_blank'
                                             );
                                          }
                                       }}
                                       className='border border-gray-300 bg-white text-gray-600 hover:text-gray-900 hover:border-gray-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                    >
                                       View
                                    </Button>
                                 )}
                              </div>

                              {/* Action buttons based on document status */}
                              <div className='flex items-center justify-end gap-2'>
                                 {/* Pending: Show Approve & Reject */}
                                 {document.driverDocument.status === 'PENDING' && (
                                    <>
                                       <Button
                                          size='sm'
                                          variant='outline'
                                          onClick={() =>
                                             setApprovingDocument({
                                                id: document.driverDocument!.id,
                                                name: document.name,
                                             })
                                          }
                                          className='border border-green-300 bg-green-50 text-green-700 hover:text-green-800 hover:border-green-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                       >
                                          Approve
                                       </Button>
                                       <Button
                                          size='sm'
                                          variant='outline'
                                          onClick={() =>
                                             setRejectingDocument({
                                                id: document.driverDocument!.id,
                                                name: document.name,
                                             })
                                          }
                                          className='border border-red-300 bg-red-50 text-red-700 hover:text-red-800 hover:border-red-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                       >
                                          Reject
                                       </Button>
                                    </>
                                 )}

                                 {/* Approved: Show Reject */}
                                 {document.driverDocument.status === 'APPROVED' && (
                                    <Button
                                       size='sm'
                                       variant='outline'
                                       onClick={() =>
                                          setRejectingDocument({
                                             id: document.driverDocument!.id,
                                             name: document.name,
                                          })
                                       }
                                       className='border border-red-300 bg-red-50 text-red-700 hover:text-red-800 hover:border-red-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                    >
                                       Reject
                                    </Button>
                                 )}

                                 {/* Rejected: Show Approve */}
                                 {document.driverDocument.status === 'REJECTED' && (
                                    <Button
                                       size='sm'
                                       variant='outline'
                                       onClick={() =>
                                          setApprovingDocument({
                                             id: document.driverDocument!.id,
                                             name: document.name,
                                          })
                                       }
                                       className='border border-green-300 bg-green-50 text-green-700 hover:text-green-800 hover:border-green-400 text-sm font-medium rounded-md px-3 py-1 transition-colors'
                                    >
                                       Approve
                                    </Button>
                                 )}
                              </div>
                           </div>
                        </div>
                     ) : (
                        /* No document uploaded yet - Default state for non-NOC documents */
                        !isNocDocument(document.identifier) && (
                           <div className='text-center py-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50'>
                              <FileText className='w-8 h-8 text-gray-400 mx-auto mb-2' />
                              <p className='text-sm text-gray-600'>No document uploaded</p>
                           </div>
                        )
                     )}
                  </Card>
               ))}
            </div>
         )}

         {/* Modals */}
         {approvingDocument && (
            <ApproveVehicleDocumentModal
               isOpen={approvingDocument !== null}
               onClose={handleModalClose}
               documentId={approvingDocument.id}
               documentName={approvingDocument.name}
            />
         )}

         {rejectingDocument && (
            <RejectVehicleDocumentModal
               isOpen={rejectingDocument !== null}
               onClose={handleModalClose}
               documentId={rejectingDocument.id}
               documentName={rejectingDocument.name}
            />
         )}
      </div>
   );
}
