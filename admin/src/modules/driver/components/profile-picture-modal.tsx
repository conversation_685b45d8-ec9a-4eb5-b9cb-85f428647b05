'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { FileUploadSection } from '@/components/file-upload-section';
import { Spinner } from '@/components/ui/spinner';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useUpdateDriver } from '../api/mutations';

interface ProfilePictureModalProps {
   driverId: string;
   currentProfilePicture?: string | null;
   driverName: string;
   isOpen: boolean;
   onClose: () => void;
}

export const ProfilePictureModal = ({
   driverId,
   currentProfilePicture,
   driverName,
   isOpen,
   onClose,
}: ProfilePictureModalProps) => {
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveImage, setShouldRemoveImage] = useState(false);
   const updateDriverMutation = useUpdateDriver();
   const fileUploadMutation = useFileUploadMutation();
   const queryClient = useQueryClient();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
         addFiles,
         handleFileChange,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Clear files and errors when modal opens
   useEffect(() => {
      if (isOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveImage(false);
      }
   }, [isOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveImage(false); // Reset remove flag when new file is selected
      }
   }, [file]);

   const onSubmit = async () => {
      setFileError('');

      try {
         let profilePictureUrl: string | null;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            profilePictureUrl = uploadResponse.data.url;
         } else if (shouldRemoveImage) {
            profilePictureUrl = null;
         } else {
            // Keep existing image if no new file and not removing
            return; // No changes to make
         }

         updateDriverMutation.mutate(
            { id: driverId, profilePictureUrl },
            {
               onSuccess: () => {
                  const message = file
                     ? 'Profile picture updated successfully'
                     : shouldRemoveImage
                     ? 'Profile picture removed successfully'
                     : 'No changes made';
                  toast.success(message);
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['driver', driverId] });
               },
               onError: (error: any) => {
                  console.error('Update error:', error);
                  toast.error('Failed to update profile picture');
               },
            }
         );
      } catch (error: any) {
         console.error('Submit error:', error);
         setFileError('Failed to upload image. Please try again.');
      }
   };

   const handleClose = () => {
      onClose();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveImage(false);
   };

   const isLoading = updateDriverMutation.isPending || fileUploadMutation.isPending;

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-md'
         >
            <DialogHeader>
               <DialogTitle>Update Profile Picture</DialogTitle>
               <DialogDescription>
                  Upload or remove profile picture for {driverName}
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-4 py-4'>
               {/* File Upload Section */}
               <FileUploadSection
                  label='Profile Picture'
                  fieldName='profilePicture'
                  maxSize={maxSize}
                  existingFileUrl={currentProfilePicture}
                  fileError={fileError}
                  fileUploadState={{ files, isDragging, errors: uploadErrors }}
                  fileUploadActions={{
                     handleDragEnter,
                     handleDragLeave,
                     handleDragOver,
                     handleDrop,
                     openFileDialog,
                     removeFile,
                     getInputProps,
                     addFiles,
                     clearFiles,
                     clearErrors,
                     handleFileChange,
                  }}
                  
                  // Enhanced removal functionality
                  onRemoveExisting={() => setShouldRemoveImage(true)}
                  isExistingFileMarkedForRemoval={shouldRemoveImage}
                  showRemovalIndicator={true}
               />
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='button' onClick={onSubmit} disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {file ? 'Uploading...' : shouldRemoveImage ? 'Removing...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : file ? (
                     'Update Picture'
                  ) : shouldRemoveImage ? (
                     'Remove Picture'
                  ) : (
                     'Save'
                  )}
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
};