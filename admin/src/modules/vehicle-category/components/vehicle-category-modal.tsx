'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { FileUploadSection } from '@/components/file-upload-section';
import { toast } from '@/lib/toast';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateVehicleCategory, useUpdateVehicleCategory } from '../api/mutations';
import { useGetVehicleCategory } from '../api/queries';

const vehicleCategorySchema = z.object({
   name: z
      .string()
      .min(1, 'Vehicle category name is required')
      .min(2, 'Vehicle category name must be at least 2 characters')
      .max(100, 'Vehicle category name must not exceed 100 characters')
      .regex(/^[a-zA-Z\s]+$/, 'Category name should contain only letters and spaces.'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
});

type VehicleCategoryFormValues = z.infer<typeof vehicleCategorySchema>;

interface VehicleCategoryModalProps {
   vehicleCategoryId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const VehicleCategoryModal = ({
   vehicleCategoryId,
   isOpen,
   onClose,
   mode = 'create',
}: VehicleCategoryModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveImage, setShouldRemoveImage] = useState(false);
   const createVehicleCategoryMutation = useCreateVehicleCategory();
   const updateVehicleCategoryMutation = useUpdateVehicleCategory();
   const vehicleCategoryQuery = useGetVehicleCategory(vehicleCategoryId || null);
   const fileUploadMutation = useFileUploadMutation();
   const queryClient = useQueryClient();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<VehicleCategoryFormValues>({
      resolver: zodResolver(vehicleCategorySchema),
      defaultValues: {
         name: '',
         description: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
         addFiles,
         handleFileChange,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Reset form when vehicleCategoryId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && vehicleCategoryQuery.data?.data) {
         const vehicleCategory = vehicleCategoryQuery.data.data;
         reset({
            name: vehicleCategory.name,
            description: vehicleCategory.description || '',
         });
      } else if (mode === 'create') {
         reset({
            name: '',
            description: '',
         });
      }
   }, [vehicleCategoryQuery.data, reset, mode]);

   // Clear files and errors when modal opens
   useEffect(() => {
      if (modalOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveImage(false);
      }
   }, [modalOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveImage(false); // Reset remove flag when new file is selected
      }
   }, [file]);

   const onSubmit = async (data: VehicleCategoryFormValues) => {
      setFileError('');

      try {
         let imageUrl: string | null | undefined;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            imageUrl = uploadResponse.data.key;
         } else if (shouldRemoveImage) {
            imageUrl = null;
         } else if (mode === 'edit' && vehicleCategoryQuery.data?.data?.image) {
            imageUrl = vehicleCategoryQuery.data.data.image;
         }

         const payload: any = {
            name: data.name,
            description: data.description || undefined,
         };

         if (file || shouldRemoveImage) {
            payload.image = imageUrl;
         }

         if (mode === 'create') {
            createVehicleCategoryMutation.mutate(payload, {
               onSuccess: () => {
                  toast.success('Vehicle category created successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['vehicle-categories'] });
               },
            });
         } else if (mode === 'edit' && vehicleCategoryId) {
            updateVehicleCategoryMutation.mutate(
               { id: vehicleCategoryId, ...payload },
               {
                  onSuccess: () => {
                     toast.success('Vehicle category updated successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['vehicle-categories'] });
                     queryClient.invalidateQueries({
                        queryKey: ['vehicle-category', vehicleCategoryId],
                     });
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveImage(false);
   };

   const isLoading =
      mode === 'create'
         ? createVehicleCategoryMutation.isPending || fileUploadMutation.isPending
         : updateVehicleCategoryMutation.isPending || fileUploadMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && vehicleCategoryQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>
                     Please wait while we load the vehicle category data.
                  </DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && vehicleCategoryQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load vehicle category data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load vehicle category data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-md'
      >
         <DialogHeader>
            <DialogTitle>
               {mode === 'create' ? 'Create New Vehicle Category' : 'Edit Vehicle Category'}
            </DialogTitle>
            <DialogDescription>
               {mode === 'create'
                  ? 'Add a new vehicle category to the system'
                  : 'Update the vehicle category information'}
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Vehicle Category Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. SUV, Sedan, Truck'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='description'>Description</Label>
               <Controller
                  control={control}
                  name='description'
                  render={({ field }) => (
                     <Textarea
                        id='description'
                        placeholder='Enter vehicle category description'
                        {...field}
                        className='w-full min-h-[80px] resize-none'
                     />
                  )}
               />
               {errors.description && <ErrorMessage error={errors.description} />}
            </div>

            {/* Image Upload Section */}
            <FileUploadSection
               label='Vehicle Category Image'
               fieldName='image'
               maxSize={maxSize}
               existingFileUrl={mode === 'edit' ? vehicleCategoryQuery.data?.data?.image : undefined}
               fileError={fileError}
               fileUploadState={{ files, isDragging, errors: uploadErrors }}
               fileUploadActions={{
                  handleDragEnter,
                  handleDragLeave,
                  handleDragOver,
                  handleDrop,
                  openFileDialog,
                  removeFile,
                  getInputProps,
                  addFiles,
                  clearFiles,
                  clearErrors,
                  handleFileChange,
               }}
               
               // Enhanced removal functionality
               onRemoveExisting={() => setShouldRemoveImage(true)}
               isExistingFileMarkedForRemoval={shouldRemoveImage}
               showRemovalIndicator={true}
            />

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : mode === 'create' ? (
                     'Create Vehicle Category'
                  ) : (
                     'Update Vehicle Category'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, wrap with trigger
   if (mode === 'create' && isOpen === undefined) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogTrigger asChild>
               <Button className='cursor-pointer' variant='outline'>
                  <Plus />
                  Add Vehicle Category
               </Button>
            </DialogTrigger>
            {content}
         </Dialog>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};
