'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { formatBytes, type FileUploadState, type FileUploadActions } from '@/hooks/use-file-upload';
import { openFilePreview } from '@/lib/file-preview';
import { Upload, X, FileText, AlertCircle, File, Loader2 } from 'lucide-react';
import Image from 'next/image';
import React, { useState } from 'react';

interface FileUploadSectionProps {
   // Core props
   label: string;
   fieldName: string;
   maxSize: number;
   existingFileUrl?: string | null;
   fileError: string;

   // File upload hook results (grouped)
   fileUploadState: FileUploadState;
   fileUploadActions: FileUploadActions;

   // Enhanced props for removal functionality (optional - for backwards compatibility)
   onRemoveExisting?: () => void; // Callback when existing file should be removed
   isExistingFileMarkedForRemoval?: boolean; // Show removal state
   showRemovalIndicator?: boolean; // Show removal notification UI

   // Custom text props (optional - for flexibility)
   uploadText?: string; // Custom upload button text (e.g. "Upload Supporting Document")
   uploadDescription?: string; // Custom description text (e.g. "Upload a supporting document...")
   supportedTypesText?: string; // Custom supported types text (e.g. "Supported: JPEG, PNG, PDF")

   // KYC mode - for documents that should always have a file present
   requiresFile?: boolean; // When true, existing files show "Edit" instead of "X"

   // Direct submission functionality (optional)
   enableDirectSubmission?: boolean; // When true, shows submit button for selected files
   onDirectSubmit?: (file?: File) => void; // Callback for direct submission
   isSubmitting?: boolean; // Show loading state for submit button
   submitButtonText?: string; // Custom submit button text (default: "Submit")
}

// File type detection utility
const getFileType = (file: File | string | any): 'image' | 'pdf' | 'other' => {
   if (typeof file === 'string') {
      // Existing file URL - handle S3 URLs and query parameters
      // First try to extract filename from URL path (before query params)
      const urlPath = file.split('?')[0]; // Remove query parameters
      const pathSegments = urlPath.split('/');
      const fileName = pathSegments[pathSegments.length - 1];

      // Check if URL contains image indicators in the filename or path
      const imagePatterns = /\.(jpg|jpeg|png|gif|webp|svg)$/i;
      const pdfPatterns = /\.pdf$/i;

      // Also check for patterns like "image.png" in S3 URLs
      const hasImageInUrl = /image\.(jpg|jpeg|png|gif|webp|svg)/i.test(file);
      const hasPdfInUrl = /\.pdf/i.test(file);

      if (imagePatterns.test(fileName) || hasImageInUrl) {
         return 'image';
      } else if (pdfPatterns.test(fileName) || hasPdfInUrl) {
         return 'pdf';
      }

      return 'other';
   } else if (file && typeof file === 'object') {
      // File object or FileMetadata - check MIME type or name
      const fileType = file.type || '';
      const fileName = file.name || '';

      if (fileType.startsWith('image/')) return 'image';
      if (fileType === 'application/pdf') return 'pdf';

      // Fallback to extension check for FileMetadata
      const extension = fileName.split('.').pop()?.toLowerCase();
      if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
         return 'image';
      } else if (extension === 'pdf') {
         return 'pdf';
      }

      return 'other';
   }
   return 'other';
};

// FilePreview component for showing file type-specific previews
interface FilePreviewProps {
   file?: { file: File | any; preview?: string } | null;
   existingFileUrl?: string | null;
   fileName: string;
}

const FilePreview: React.FC<FilePreviewProps> = ({ file, existingFileUrl, fileName }) => {
   const [imageError, setImageError] = useState(false);

   // Determine file type and preview source
   const fileType = file
      ? getFileType(file.file)
      : existingFileUrl
      ? getFileType(existingFileUrl)
      : 'other';
   const previewSrc = file?.preview || existingFileUrl;

   // Always try to show image if we think it's an image, but fall back gracefully on error
   if (fileType === 'image' && previewSrc && !imageError) {
      return (
         <div className='bg-accent aspect-square shrink-0 rounded overflow-hidden w-10 h-10 relative'>
            <Image
               src={previewSrc}
               alt={fileName}
               fill
               className='object-cover'
               onError={() => {
                  console.log('Image load error for:', previewSrc);
                  setImageError(true);
               }}
               onLoad={() => setImageError(false)}
               sizes='40px'
               unoptimized={previewSrc?.startsWith('blob:') || !previewSrc?.startsWith('http')}
            />
         </div>
      );
   } else if (fileType === 'pdf' && !imageError) {
      // Show PDF icon for PDF files
      return (
         <div className='flex items-center justify-center w-10 h-10 bg-red-100 rounded-lg'>
            <FileText className='w-5 h-5 text-red-600' />
         </div>
      );
   } else {
      // Default file icon for other types or failed image loads
      return (
         <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
            <File className='w-5 h-5 text-blue-600' />
         </div>
      );
   }
};

export const FileUploadSection = ({
   // Core props
   label,
   fieldName,
   maxSize,
   existingFileUrl,
   fileError,

   // File upload hook results
   fileUploadState,
   fileUploadActions,

   // Enhanced removal functionality props
   onRemoveExisting,
   isExistingFileMarkedForRemoval = false,
   showRemovalIndicator = false,

   // Custom text props
   uploadText,
   uploadDescription,
   supportedTypesText,

   // KYC mode
   requiresFile = false,

   // Direct submission functionality
   enableDirectSubmission = false,
   onDirectSubmit,
   isSubmitting = false,
   submitButtonText = 'Submit',
}: FileUploadSectionProps) => {
   // Destructure hook results for easier access
   const { files, isDragging, errors: uploadErrors } = fileUploadState;
   const {
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      removeFile,
      getInputProps,
   } = fileUploadActions;

   const file = files[0];
   return (
      <div className='space-y-4'>
         <Label htmlFor={fieldName}>{label}</Label>

         {/* File Upload Area */}
         <div
            role='button'
            onClick={openFileDialog}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            data-dragging={isDragging || undefined}
            className='border-input cursor-pointer hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-32 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
         >
            <input
               {...getInputProps()}
               className='sr-only'
               aria-label={`Upload ${fieldName}`}
               disabled={Boolean(file)}
            />

            <div className='flex flex-col items-center justify-center text-center'>
               <div
                  className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                  aria-hidden='true'
               >
                  <Upload className='size-4 opacity-60' />
               </div>
               <p className='mb-1.5 text-sm font-medium'>
                  {uploadText || `Upload ${label.split(' ').slice(-1)[0]}`}
               </p>
               <p className='text-muted-foreground text-xs'>
                  {uploadDescription ||
                     `Drag & drop or click to browse (max. ${formatBytes(maxSize)})`}
               </p>
               <p className='text-muted-foreground text-xs mt-1'>
                  {supportedTypesText || 'Supported: JPEG, PNG'}
               </p>
            </div>
         </div>

         {/* Upload Errors */}
         {uploadErrors.length > 0 && (
            <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
               <AlertCircle className='size-3 shrink-0' />
               <span>{uploadErrors[0]}</span>
            </div>
         )}
         {fileError && (
            <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
               <AlertCircle className='size-3 shrink-0' />
               <span>{fileError}</span>
            </div>
         )}

         {/* File Preview */}
         {(file || (existingFileUrl && !isExistingFileMarkedForRemoval)) && (
            <Card className='p-4'>
               <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-3'>
                     <FilePreview
                        file={file}
                        existingFileUrl={existingFileUrl}
                        fileName={
                           file
                              ? file.file.name || 'Unknown file'
                              : `Current ${label.split(' ').slice(-1)[0]}`
                        }
                     />
                     <div>
                        {file ? (
                           <>
                              <p className='text-sm font-medium text-gray-900'>
                                 {file.file.name || 'Unknown file'}
                              </p>
                              <p className='text-xs text-gray-500'>
                                 {formatBytes(file.file.size || 0)}
                              </p>
                           </>
                        ) : (
                           <>
                              <p className='text-sm font-medium text-gray-900'>
                                 Current {label.split(' ').slice(-1)[0]}
                              </p>
                              <p className='text-xs text-gray-500'>Uploaded {fieldName}</p>
                           </>
                        )}
                     </div>
                  </div>
                  <div className='flex items-center gap-2'>
                     {/* View button for both new files (blob URL) and existing files (S3 URL) */}
                     {(file?.preview || existingFileUrl) && (
                        <Button
                           type='button'
                           variant='outline'
                           size='sm'
                           onClick={() => {
                              const fileType = file
                                 ? getFileType(file.file)
                                 : existingFileUrl
                                 ? getFileType(existingFileUrl)
                                 : 'other';

                              const viewUrl = file?.preview || existingFileUrl;

                              if (viewUrl && fileType) {
                                 openFilePreview(viewUrl, fileType);
                              }
                           }}
                           className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                        >
                           View
                        </Button>
                     )}

                     {/* Direct Submit button - only show for new files when enabled */}
                     {enableDirectSubmission && file && onDirectSubmit && (
                        <Button
                           type='button'
                           size='sm'
                           onClick={() => onDirectSubmit(file.file as File)}
                           disabled={isSubmitting}
                           className='bg-blue-600 hover:bg-blue-700'
                        >
                           {isSubmitting ? (
                              <>
                                 <Loader2 className='w-4 h-4 animate-spin mr-2' />
                                 Uploading...
                              </>
                           ) : (
                              <>
                                 <Upload className='w-4 h-4 mr-2' />
                                 {submitButtonText}
                              </>
                           )}
                        </Button>
                     )}

                     {/* Remove/Edit button logic */}
                     {file ? (
                        // New file selected - show X button to remove and revert to existing file
                        <Button
                           type='button'
                           variant='ghost'
                           size='sm'
                           onClick={() => removeFile(file.id)}
                           className='text-red-600 hover:text-red-700'
                        >
                           <X className='w-4 h-4' />
                        </Button>
                     ) : existingFileUrl ? (
                        // Existing file - show Edit button if requiresFile is true, otherwise show X
                        <Button
                           type='button'
                           variant='outline'
                           size='sm'
                           onClick={() => {
                              if (requiresFile) {
                                 // Open file picker to replace existing file
                                 openFileDialog();
                              } else if (onRemoveExisting) {
                                 // Mark existing file for removal
                                 onRemoveExisting();
                              }
                           }}
                           className={
                              requiresFile
                                 ? 'text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                                 : 'text-red-600 hover:text-red-700'
                           }
                        >
                           {requiresFile ? 'Edit' : <X className='w-4 h-4' />}
                        </Button>
                     ) : null}
                  </div>
               </div>
            </Card>
         )}

         {/* Removal Indicator */}
         {showRemovalIndicator && isExistingFileMarkedForRemoval && !file && (
            <div className='text-sm text-gray-600 bg-red-50 border border-red-200 rounded-lg p-3'>
               Current {fieldName} will be removed when you save.
            </div>
         )}
      </div>
   );
};
