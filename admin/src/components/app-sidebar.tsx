'use client';
import * as React from 'react';
import { Car, LayoutDashboard, Languages, Package, Truck, ShoppingBag, MapPin } from 'lucide-react';
import { usePathname } from 'next/navigation';

import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { LogoName } from './logo-name';

// Driver management data
const data = {
   user: {
      name: 'john doe',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
   },
   navMain: [
      {
         title: 'Dashboard',
         url: '/dashboard/home',
         icon: LayoutDashboard,
         isActive: false,
      },
      {
         title: 'Drivers',
         url: '/dashboard/drivers',
         icon: Car,
         isActive: false,
      },
      {
         title: 'Cities',
         url: '/dashboard/cities',
         icon: MapPin,
         isActive: false,
      },
      {
         title: 'Languages',
         url: '/dashboard/languages',
         icon: Languages,
         isActive: false,
      },
      {
         title: 'Products',
         url: '/dashboard/products',
         icon: ShoppingBag,
         isActive: false,
      },
      {
         title: 'Product Services',
         url: '/dashboard/product-services',
         icon: Package,
         isActive: false,
      },
      {
         title: 'Vehicle Categories',
         url: '/dashboard/vehicle-categories',
         icon: Truck,
         isActive: false,
      },
   ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const pathname = usePathname();

   // Update navMain items with active state based on current pathname
   const navMainWithActiveState = data.navMain.map(item => ({
      ...item,
      isActive: pathname === item.url || pathname.startsWith(item.url + '/'),
   }));

   return (
      <Sidebar className='bg-white border-r border-gray-200' collapsible='icon' {...props}>
         <SidebarHeader className='bg-white border-b border-gray-200 p-3 group-data-[collapsible=icon]:p-1 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:h-12'>
            <div className='flex items-center justify-center'>
               <LogoName width={60} height={60} />
            </div>
         </SidebarHeader>
         <SidebarContent className='bg-white p-2 group-data-[collapsible=icon]:p-1'>
            <NavMain items={navMainWithActiveState} />
         </SidebarContent>
         <SidebarRail />
      </Sidebar>
   );
}
