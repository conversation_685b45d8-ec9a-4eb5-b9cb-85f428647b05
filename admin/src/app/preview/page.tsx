'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';

function PreviewContent() {
   const searchParams = useSearchParams();
   const url = searchParams.get('url');

   if (!url) {
      return (
         <div className="flex items-center justify-center h-screen bg-gray-100">
            <div className="text-center">
               <h1 className="text-2xl font-semibold text-gray-800 mb-2">No File URL Provided</h1>
               <p className="text-gray-600">Please provide a valid file URL to preview.</p>
            </div>
         </div>
      );
   }

   return (
      <div className="h-screen w-full flex items-center justify-center">
         <embed
            src={url}
            className="w-full h-full border-none"
            title="File Preview"
         />
      </div>
   );
}

export default function PreviewPage() {
   return (
      <Suspense fallback={
         <div className="flex items-center justify-center h-screen bg-gray-100">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
               <p className="text-gray-600">Loading preview...</p>
            </div>
         </div>
      }>
         <PreviewContent />
      </Suspense>
   );
}